
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>模型性能比较报告</title>
        <style>
            body {
                font-family: 'Arial', sans-serif;
                margin: 40px;
                background-color: #f5f5f5;
            }
            .container {
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #2c3e50;
                text-align: center;
                border-bottom: 3px solid #3498db;
                padding-bottom: 10px;
            }
            h2 {
                color: #34495e;
                margin-top: 30px;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 12px;
                text-align: center;
            }
            th {
                background-color: #3498db;
                color: white;
                font-weight: bold;
            }
            tr:nth-child(even) {
                background-color: #f2f2f2;
            }
            .best-score {
                background-color: #2ecc71 !important;
                color: white;
                font-weight: bold;
            }
            .summary {
                background-color: #ecf0f1;
                padding: 20px;
                border-radius: 5px;
                margin: 20px 0;
            }
            .metric-description {
                font-size: 0.9em;
                color: #7f8c8d;
                margin-top: 10px;
            }
            .strategy-explanation {
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                border: 1px solid #dee2e6;
            }
            .strategy-explanation h3 {
                color: #495057;
                margin-top: 20px;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 2px solid #e9ecef;
            }
            .strategy-explanation ul {
                margin: 10px 0;
                padding-left: 20px;
            }
            .strategy-explanation li {
                margin: 5px 0;
                line-height: 1.4;
            }
            .strategy-explanation p {
                margin: 10px 0;
                line-height: 1.5;
            }
            .strategy-explanation em {
                color: #6c757d;
                font-size: 0.9em;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>模型性能比较报告</h1>
            <p style="text-align: center; color: #7f8c8d;">生成时间: 2025-08-09 03:11:53</p>
            
            <div class="summary">
                <h2>📊 执行摘要</h2>
                <p><strong>最佳模型 (平衡策略):</strong> NeuralNet (综合得分: 0.619)</p>
                <p><strong>比较模型数量:</strong> 3</p>
                <p><strong>评估指标数量:</strong> 13</p>
                <p><strong>评估策略数量:</strong> 4 (性能优先、稳健性优先、平衡策略、可解释性优先)</p>
            </div>

            <h2>📋 评分策略说明</h2>
            <div class="strategy-explanation">
                <h3>🎯 性能优先策略 (Performance)</h3>
                <p>注重模型的整体预测性能，适用于对准确性要求极高的场景。</p>
                <ul>
                    <li>AUC-ROC: 30% - 衡量分类器整体区分能力</li>
                    <li>F1-Score: 25% - 平衡精确率和召回率</li>
                    <li>准确率: 20% - 整体预测正确率</li>
                    <li>精确率: 15% - 预测为正例的准确性</li>
                    <li>MCC: 10% - 马修斯相关系数，适合不平衡数据</li>
                </ul>

                <h3>🛡️ 稳健性优先策略 (Robustness)</h3>
                <p>注重模型的稳定性和可靠性，适用于数据质量不稳定或不平衡的场景。</p>
                <ul>
                    <li>MCC: 30% - 马修斯相关系数，对不平衡数据敏感</li>
                    <li>平衡准确率: 25% - 考虑类别平衡的准确率</li>
                    <li>Kappa系数: 20% - 衡量分类一致性</li>
                    <li>F1-Score: 15% - 综合性能指标</li>
                    <li>AUC-ROC: 10% - 基础区分能力</li>
                </ul>

                <h3>⚖️ 平衡策略 (Balanced)</h3>
                <p>综合考虑各项指标，适用于大多数应用场景的通用选择。</p>
                <ul>
                    <li>AUC-ROC: 25% - 整体区分能力</li>
                    <li>F1-Score: 20% - 综合性能</li>
                    <li>MCC: 20% - 稳健性指标</li>
                    <li>准确率: 15% - 基础性能</li>
                    <li>精确率: 10% - 预测准确性</li>
                    <li>召回率: 10% - 覆盖完整性</li>
                </ul>

                <h3>🔍 可解释性优先策略 (Interpretability)</h3>
                <p>在保证性能的同时，优先选择易于理解和解释的模型。</p>
                <ul>
                    <li>F1-Score: 30% - 基础性能保证</li>
                    <li>准确率: 25% - 整体表现</li>
                    <li>可解释性得分: 25% - 模型复杂度和可理解性</li>
                    <li>AUC-ROC: 20% - 区分能力</li>
                </ul>
                <p><em>可解释性得分：决策树(0.9) > 逻辑回归/朴素贝叶斯(0.8) > 随机森林(0.7) > KNN(0.6) > SVM(0.5) > 集成模型(0.4) > 神经网络(0.3)</em></p>
            </div>

            <h2>🏆 模型排名 (平衡策略)</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>综合得分</th>
                    <th>准确率</th>
                    <th>精确率</th>
                    <th>召回率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                </tr>
    
                <tr>
                    <td>1</td>
                    <td><strong>NeuralNet</strong></td>
                    <td class="best-score">0.619</td>
                    <td>0.875</td>
                    <td>0.875</td>
                    <td>0.824</td>
                    <td>0.848</td>
                    <td>0.000</td>
                </tr>
        
                <tr>
                    <td>2</td>
                    <td><strong>RandomForest</strong></td>
                    <td class="">0.619</td>
                    <td>0.875</td>
                    <td>0.875</td>
                    <td>0.824</td>
                    <td>0.848</td>
                    <td>0.000</td>
                </tr>
        
                <tr>
                    <td>3</td>
                    <td><strong>SVM</strong></td>
                    <td class="">0.515</td>
                    <td>0.775</td>
                    <td>0.750</td>
                    <td>0.706</td>
                    <td>0.727</td>
                    <td>0.000</td>
                </tr>
        
            </table>
    
            <h2>🎯 性能优先策略排名</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>策略得分</th>
                    <th>准确率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                    <th>MCC</th>
                </tr>
                
                <tr>
                    <td>1</td>
                    <td><strong>NeuralNet</strong></td>
                    <td class="best-score">0.593</td>
                    <td>0.875</td>
                    <td>0.848</td>
                    <td>0.000</td>
                    <td>0.743</td>
                </tr>
                    
                <tr>
                    <td>2</td>
                    <td><strong>RandomForest</strong></td>
                    <td class="">0.593</td>
                    <td>0.875</td>
                    <td>0.848</td>
                    <td>0.000</td>
                    <td>0.743</td>
                </tr>
                    
                <tr>
                    <td>3</td>
                    <td><strong>SVM</strong></td>
                    <td class="">0.503</td>
                    <td>0.775</td>
                    <td>0.727</td>
                    <td>0.000</td>
                    <td>0.537</td>
                </tr>
                    
            </table>
                
            <h2>🛡️ 稳健性优先策略排名</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>策略得分</th>
                    <th>准确率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                    <th>MCC</th>
                </tr>
                
                <tr>
                    <td>1</td>
                    <td><strong>NeuralNet</strong></td>
                    <td class="best-score">0.716</td>
                    <td>0.875</td>
                    <td>0.848</td>
                    <td>0.000</td>
                    <td>0.743</td>
                </tr>
                    
                <tr>
                    <td>2</td>
                    <td><strong>RandomForest</strong></td>
                    <td class="">0.716</td>
                    <td>0.875</td>
                    <td>0.848</td>
                    <td>0.000</td>
                    <td>0.743</td>
                </tr>
                    
                <tr>
                    <td>3</td>
                    <td><strong>SVM</strong></td>
                    <td class="">0.569</td>
                    <td>0.775</td>
                    <td>0.727</td>
                    <td>0.000</td>
                    <td>0.537</td>
                </tr>
                    
            </table>
                
            <h2>⚖️ 平衡策略排名</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>策略得分</th>
                    <th>准确率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                    <th>MCC</th>
                </tr>
                
                <tr>
                    <td>1</td>
                    <td><strong>NeuralNet</strong></td>
                    <td class="best-score">0.619</td>
                    <td>0.875</td>
                    <td>0.848</td>
                    <td>0.000</td>
                    <td>0.743</td>
                </tr>
                    
                <tr>
                    <td>2</td>
                    <td><strong>RandomForest</strong></td>
                    <td class="">0.619</td>
                    <td>0.875</td>
                    <td>0.848</td>
                    <td>0.000</td>
                    <td>0.743</td>
                </tr>
                    
                <tr>
                    <td>3</td>
                    <td><strong>SVM</strong></td>
                    <td class="">0.515</td>
                    <td>0.775</td>
                    <td>0.727</td>
                    <td>0.000</td>
                    <td>0.537</td>
                </tr>
                    
            </table>
                
            <h2>🔍 可解释性优先策略排名</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>策略得分</th>
                    <th>准确率</th>
                    <th>F1分数</th>
                    <th>AUC-ROC</th>
                    <th>MCC</th>
                </tr>
                
                <tr>
                    <td>1</td>
                    <td><strong>NeuralNet</strong></td>
                    <td class="best-score">0.473</td>
                    <td>0.875</td>
                    <td>0.848</td>
                    <td>0.000</td>
                    <td>0.743</td>
                </tr>
                    
                <tr>
                    <td>2</td>
                    <td><strong>RandomForest</strong></td>
                    <td class="">0.473</td>
                    <td>0.875</td>
                    <td>0.848</td>
                    <td>0.000</td>
                    <td>0.743</td>
                </tr>
                    
                <tr>
                    <td>3</td>
                    <td><strong>SVM</strong></td>
                    <td class="">0.412</td>
                    <td>0.775</td>
                    <td>0.727</td>
                    <td>0.000</td>
                    <td>0.537</td>
                </tr>
                    
            </table>
                
            <h2>📈 详细性能指标</h2>
            <table>
                <tr>
                    <th>模型</th>
    <th>准确率</th><th>精确率</th><th>召回率</th><th>F1分数</th><th>特异性</th><th>敏感性</th><th>阴性预测值</th><th>阳性预测值</th><th>AUC-ROC</th><th>AUC-PR</th><th>MCC</th><th>Kappa</th><th>平衡准确率</th></tr><tr><td><strong>NeuralNet</strong></td><td class="best-score">0.875</td><td class="best-score">0.875</td><td class="best-score">0.824</td><td class="best-score">0.913</td><td class="best-score">0.824</td><td class="best-score">0.875</td><td class="best-score">0.875</td><td class="best-score">0.000</td><td class="best-score">0.000</td><td class="best-score">0.743</td><td class="best-score">0.742</td><td class="best-score">0.868</td></tr><tr><td><strong>SVM</strong></td><td class="">0.775</td><td class="">0.750</td><td class="">0.706</td><td class="">0.826</td><td class="">0.706</td><td class="">0.792</td><td class="">0.750</td><td class="best-score">0.000</td><td class="best-score">0.000</td><td class="">0.537</td><td class="">0.536</td><td class="">0.766</td></tr><tr><td><strong>RandomForest</strong></td><td class="best-score">0.875</td><td class="best-score">0.875</td><td class="best-score">0.824</td><td class="best-score">0.913</td><td class="best-score">0.824</td><td class="best-score">0.875</td><td class="best-score">0.875</td><td class="best-score">0.000</td><td class="best-score">0.000</td><td class="best-score">0.743</td><td class="best-score">0.742</td><td class="best-score">0.868</td></tr>
            </table>
            
            <div class="metric-description">
                <h2>📝 指标说明</h2>
                <ul>
                    <li><strong>准确率(Accuracy):</strong> 正确预测实例的比例</li>
                    <li><strong>精确率(Precision):</strong> 预测为正例中实际为正例的比例</li>
                    <li><strong>召回率(Recall):</strong> 实际正例中被正确预测的比例</li>
                    <li><strong>F1分数(F1 Score):</strong> 精确率和召回率的调和平均数</li>
                    <li><strong>特异性(Specificity):</strong> 实际负例中被正确预测的比例</li>
                    <li><strong>AUC-ROC:</strong> ROC曲线下面积，衡量分类器性能</li>
                    <li><strong>AUC-PR:</strong> 精确率-召回率曲线下面积</li>
                    <li><strong>MCC:</strong> 马修斯相关系数，平衡的分类性能度量</li>
                    <li><strong>Kappa:</strong> 科恩卡帕系数，考虑随机一致性</li>
                    <li><strong>平衡准确率(Balanced Accuracy):</strong> 敏感性和特异性的平均值</li>
                </ul>
            </div>
            
            <div class="summary">
                <h2>💡 推荐建议</h2>
                <p>基于<strong>平衡策略</strong>的综合性能评估，我们推荐使用 <strong>NeuralNet</strong> 模型进行后续任务。</p>
                <p>不同策略下的最佳模型可能不同，请根据您的具体需求选择合适的评估策略：</p>
                <ul>
                    <li><strong>性能优先</strong>：追求最高预测准确性</li>
                    <li><strong>稳健性优先</strong>：数据不平衡或质量不稳定时</li>
                    <li><strong>平衡策略</strong>：大多数应用场景的通用选择</li>
                    <li><strong>可解释性优先</strong>：需要理解模型决策过程时</li>
                </ul>
                <p>详细的策略权重配置和各策略排名请参考上方表格。</p>
            </div>
        </div>
    </body>
    </html>
    