2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 100
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 22:56:38 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9828
2025-08-09 22:56:42 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9844
2025-08-09 22:56:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 204, 'max_depth': 32, 'min_samples_split': 3, 'min_samples_leaf': 2, 'max_features': 'sqrt'}
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 27/100
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 22:56:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:56:56 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_225656.html
2025-08-09 22:56:57 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_225656.html
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 21.18 秒
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 100
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9860
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9892
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 153, 'max_depth': 9, 'learning_rate': 0.2676604216316705, 'subsample': 0.6253646588107045, 'colsample_bytree': 0.6510423826285919}
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9899
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/100
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 22:57:00 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_225700.html
2025-08-09 22:57:01 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_225700.html
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.04 秒
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 100
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 22:57:08 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9843
2025-08-09 22:57:08 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9883
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 281, 'max_depth': 7, 'learning_rate': 0.26511448016990713, 'feature_fraction': 0.6878665519661848, 'bagging_fraction': 0.8248376205582659}
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/100
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 22:57:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_225709.html
2025-08-09 22:57:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_225710.html
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.43 秒
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 100
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9739
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 9.117827635525941, 'clf__solver': 'lbfgs'}
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9747
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 22:57:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_225711.html
2025-08-09 22:57:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_225711.html
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.23 秒
