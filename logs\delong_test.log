2025-08-09 01:29:32 - delong_test - INFO - 开始<PERSON><PERSON><PERSON>检验，比较2个模型
2025-08-09 01:29:32 - delong_test - INFO - 比较RandomForest vs XGBoost: AUC1=0.8313, AUC2=0.8325, p=0.989689
2025-08-09 01:29:32 - delong_test - INFO - <PERSON><PERSON>ong检验完成，共1个比较
2025-08-09 01:29:32 - delong_test - INFO - <PERSON><PERSON>ong检验结果已保存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\delong_test
2025-08-09 01:52:10 - delong_test - INFO - 开始DeLong检验，比较3个模型
2025-08-09 01:52:10 - delong_test - INFO - 比较RandomForest vs SVM: AUC1=0.9412, AUC2=0.9182, p=0.670889
2025-08-09 01:52:10 - delong_test - INFO - 比较RandomForest vs KNN: AUC1=0.9412, AUC2=0.9322, p=0.870121
2025-08-09 01:52:10 - delong_test - INFO - 比较SVM vs KNN: AUC1=0.9182, AUC2=0.9322, p=0.814172
2025-08-09 01:52:10 - delong_test - INFO - DeLong检验完成，共3个比较
2025-08-09 01:55:14 - delong_test - INFO - 开始DeLong检验，比较10个模型
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs SVM: AUC1=0.9412, AUC2=0.9182, p=0.670889
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs KNN: AUC1=0.9412, AUC2=0.9322, p=0.870121
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs DecisionTree: AUC1=0.9412, AUC2=0.8951, p=0.461102
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs XGBoost: AUC1=0.9412, AUC2=0.9719, p=0.442730
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs LightGBM: AUC1=0.9412, AUC2=0.9488, p=0.871470
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs CatBoost: AUC1=0.9412, AUC2=0.9591, p=0.684015
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs Logistic: AUC1=0.9412, AUC2=0.9284, p=0.808351
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs NaiveBayes: AUC1=0.9412, AUC2=0.8977, p=0.503043
2025-08-09 01:55:14 - delong_test - INFO - 比较RandomForest vs NeuralNet: AUC1=0.9412, AUC2=0.9591, p=0.681476
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs KNN: AUC1=0.9182, AUC2=0.9322, p=0.814172
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs DecisionTree: AUC1=0.9182, AUC2=0.8951, p=0.731086
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs XGBoost: AUC1=0.9182, AUC2=0.9719, p=0.250326
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs LightGBM: AUC1=0.9182, AUC2=0.9488, p=0.564215
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs CatBoost: AUC1=0.9182, AUC2=0.9591, p=0.414906
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs Logistic: AUC1=0.9182, AUC2=0.9284, p=0.859988
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs NaiveBayes: AUC1=0.9182, AUC2=0.8977, p=0.767725
2025-08-09 01:55:14 - delong_test - INFO - 比较SVM vs NeuralNet: AUC1=0.9182, AUC2=0.9591, p=0.411875
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs DecisionTree: AUC1=0.9322, AUC2=0.8951, p=0.582415
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs XGBoost: AUC1=0.9322, AUC2=0.9719, p=0.402874
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs LightGBM: AUC1=0.9322, AUC2=0.9488, p=0.757393
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs CatBoost: AUC1=0.9322, AUC2=0.9591, p=0.597174
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs Logistic: AUC1=0.9322, AUC2=0.9284, p=0.947748
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs NaiveBayes: AUC1=0.9322, AUC2=0.8977, p=0.620483
2025-08-09 01:55:14 - delong_test - INFO - 比较KNN vs NeuralNet: AUC1=0.9322, AUC2=0.9591, p=0.594848
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs XGBoost: AUC1=0.8951, AUC2=0.9719, p=0.171602
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs LightGBM: AUC1=0.8951, AUC2=0.9488, p=0.383626
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs CatBoost: AUC1=0.8951, AUC2=0.9591, p=0.278861
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs Logistic: AUC1=0.8951, AUC2=0.9284, p=0.613404
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs NaiveBayes: AUC1=0.8951, AUC2=0.8977, p=0.973132
2025-08-09 01:55:14 - delong_test - INFO - 比较DecisionTree vs NeuralNet: AUC1=0.8951, AUC2=0.9591, p=0.276612
2025-08-09 01:55:14 - delong_test - INFO - 比较XGBoost vs LightGBM: AUC1=0.9719, AUC2=0.9488, p=0.551970
2025-08-09 01:55:14 - delong_test - INFO - 比较XGBoost vs CatBoost: AUC1=0.9719, AUC2=0.9591, p=0.710112
2025-08-09 01:55:14 - delong_test - INFO - 比较XGBoost vs Logistic: AUC1=0.9719, AUC2=0.9284, p=0.334321
2025-08-09 01:55:14 - delong_test - INFO - 比较XGBoost vs NaiveBayes: AUC1=0.9719, AUC2=0.8977, p=0.207561
2025-08-09 01:55:14 - delong_test - INFO - 比较XGBoost vs NeuralNet: AUC1=0.9719, AUC2=0.9591, p=0.706238
2025-08-09 01:55:14 - delong_test - INFO - 比较LightGBM vs CatBoost: AUC1=0.9488, AUC2=0.9591, p=0.811190
2025-08-09 01:55:14 - delong_test - INFO - 比较LightGBM vs Logistic: AUC1=0.9488, AUC2=0.9284, p=0.692589
2025-08-09 01:55:14 - delong_test - INFO - 比较LightGBM vs NaiveBayes: AUC1=0.9488, AUC2=0.8977, p=0.425151
2025-08-09 01:55:14 - delong_test - INFO - 比较LightGBM vs NeuralNet: AUC1=0.9488, AUC2=0.9591, p=0.809528
2025-08-09 01:55:14 - delong_test - INFO - 比较CatBoost vs Logistic: AUC1=0.9591, AUC2=0.9284, p=0.527935
2025-08-09 01:55:14 - delong_test - INFO - 比较CatBoost vs NaiveBayes: AUC1=0.9591, AUC2=0.8977, p=0.319359
2025-08-09 01:55:14 - delong_test - INFO - 比较CatBoost vs NeuralNet: AUC1=0.9591, AUC2=0.9591, p=1.000000
2025-08-09 01:55:14 - delong_test - INFO - 比较Logistic vs NaiveBayes: AUC1=0.9284, AUC2=0.8977, p=0.652459
2025-08-09 01:55:14 - delong_test - INFO - 比较Logistic vs NeuralNet: AUC1=0.9284, AUC2=0.9591, p=0.525075
2025-08-09 01:55:14 - delong_test - INFO - 比较NaiveBayes vs NeuralNet: AUC1=0.8977, AUC2=0.9591, p=0.317282
2025-08-09 01:55:14 - delong_test - INFO - DeLong检验完成，共45个比较
2025-08-09 03:07:25 - delong_test - INFO - 开始DeLong检验，比较3个模型
2025-08-09 03:07:25 - delong_test - INFO - 比较RandomForest vs SVM: AUC1=0.9412, AUC2=0.9182, p=0.670889
2025-08-09 03:07:25 - delong_test - INFO - 比较RandomForest vs NeuralNet: AUC1=0.9412, AUC2=0.9591, p=0.681476
2025-08-09 03:07:25 - delong_test - INFO - 比较SVM vs NeuralNet: AUC1=0.9182, AUC2=0.9591, p=0.411875
2025-08-09 03:07:25 - delong_test - INFO - DeLong检验完成，共3个比较
2025-08-09 22:57:37 - delong_test - INFO - 开始DeLong检验，比较4个模型
2025-08-09 22:57:37 - delong_test - INFO - 比较RandomForest vs XGBoost: AUC1=0.9412, AUC2=0.9719, p=0.442730
2025-08-09 22:57:37 - delong_test - INFO - 比较RandomForest vs LightGBM: AUC1=0.9412, AUC2=0.9488, p=0.871470
2025-08-09 22:57:37 - delong_test - INFO - 比较RandomForest vs Logistic: AUC1=0.9412, AUC2=0.9284, p=0.808351
2025-08-09 22:57:37 - delong_test - INFO - 比较XGBoost vs LightGBM: AUC1=0.9719, AUC2=0.9488, p=0.551970
2025-08-09 22:57:37 - delong_test - INFO - 比较XGBoost vs Logistic: AUC1=0.9719, AUC2=0.9284, p=0.334321
2025-08-09 22:57:37 - delong_test - INFO - 比较LightGBM vs Logistic: AUC1=0.9488, AUC2=0.9284, p=0.692589
2025-08-09 22:57:37 - delong_test - INFO - DeLong检验完成，共6个比较
