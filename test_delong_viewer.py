#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DeLong检验结果查看器
"""

import sys
import tkinter as tk
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def test_delong_viewer():
    """测试DeLong检验结果查看器"""
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("DeLong检验结果查看器测试")
    root.geometry("400x300")
    
    # 模拟GUI对象
    class MockGUI:
        def __init__(self):
            self.root = root
            
        def log_message(self, message):
            print(f"LOG: {message}")
    
    # 创建GUI功能对象
    from gui_functions import GUIFunctions
    mock_gui = MockGUI()
    functions = GUIFunctions(mock_gui)
    
    # 创建测试按钮
    test_frame = tk.Frame(root)
    test_frame.pack(expand=True, fill=tk.BOTH, padx=20, pady=20)
    
    tk.Label(test_frame, text="DeLong检验结果查看器测试", 
             font=("Arial", 14, "bold")).pack(pady=10)
    
    tk.Button(test_frame, text="🔬 查看DeLong检验结果", 
              command=functions.view_delong_results,
              font=("Arial", 12),
              bg="#e1f5fe", 
              relief=tk.RAISED,
              padx=20, pady=10).pack(pady=10)
    
    tk.Label(test_frame, text="点击按钮查看DeLong检验结果\n(需要先运行模型训练生成结果文件)", 
             font=("Arial", 10),
             fg="gray").pack(pady=10)
    
    tk.Button(test_frame, text="❌ 关闭", 
              command=root.destroy,
              font=("Arial", 10)).pack(pady=10)
    
    # 运行测试
    root.mainloop()

if __name__ == "__main__":
    test_delong_viewer()
