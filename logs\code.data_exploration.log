2025-08-10 00:53:56 - code.data_exploration - INFO - 数据探索器初始化完成，输出目录: test_output
2025-08-10 00:53:56 - code.data_exploration - INFO - 开始分组概率分析，变量: ['feature1_normal', 'feature2_skewed', 'feature3_discrete', 'feature4_uniform'], 目标: target
2025-08-10 00:53:56 - code.data_exploration - WARNING - 唯一值数量(5)少于请求分箱数(8)，调整为4箱
2025-08-10 00:53:56 - code.data_exploration - INFO - 等频分箱：请求4箱，实际得到3箱（由于重复值）
2025-08-10 00:53:56 - code.data_exploration - INFO - 分组概率分析完成，共分析 4 个变量
2025-08-10 00:53:56 - code.data_exploration - INFO - 开始分组概率分析，变量: ['feature1_normal', 'feature2_skewed', 'feature3_discrete', 'feature4_uniform'], 目标: target
2025-08-10 00:53:56 - code.data_exploration - INFO - 分组概率分析完成，共分析 4 个变量
2025-08-10 00:53:56 - code.data_exploration - INFO - 开始分组概率分析，变量: ['feature1_normal', 'feature2_skewed', 'feature3_discrete', 'feature4_uniform'], 目标: target
2025-08-10 00:53:56 - code.data_exploration - INFO - 分组概率分析完成，共分析 4 个变量
2025-08-10 00:53:56 - code.data_exploration - INFO - 开始分组概率分析，变量: ['feature1_normal', 'feature2_skewed', 'feature3_discrete', 'feature4_uniform'], 目标: target
2025-08-10 00:53:57 - code.data_exploration - INFO - 分组概率分析完成，共分析 4 个变量
2025-08-10 00:53:57 - code.data_exploration - INFO - 数据探索器初始化完成，输出目录: test_output
2025-08-10 00:53:57 - code.data_exploration - WARNING - 唯一值数量(3)少于请求分箱数(10)，调整为2箱
2025-08-10 00:53:57 - code.data_exploration - WARNING - 变量 feature 清理后无数据
