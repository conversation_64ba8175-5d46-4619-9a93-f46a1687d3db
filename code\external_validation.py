#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
外部验证模块 - 用于在外部数据集上验证已训练的模型

此模块允许用户:
1. 加载已训练好的模型
2. 加载外部验证数据集
3. 在外部数据集上评估模型性能
4. 生成验证报告和可视化结果
"""

import os
import sys
import argparse
import pandas as pd
import numpy as np
from pathlib import Path
from joblib import load, dump
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    roc_curve, auc, confusion_matrix, classification_report,
    roc_auc_score, precision_recall_curve, average_precision_score
)
from sklearn.calibration import calibration_curve
import json

# 动态添加当前目录到 Python 路径，确保模块可以被找到
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入项目模块
try:
    from config import OUTPUT_PATH, CACHE_PATH, DATA_PATH, MODEL_NAMES
    from logger import get_default_logger
    from plot_single_model import plot_model_visualizations
    from data_preprocessing import validate_and_load_data
    from plot_utils import translate_term, get_font_properties
except ImportError:
    # 如果导入失败，使用基本配置
    from pathlib import Path
    OUTPUT_PATH = Path("../output")
    CACHE_PATH = Path("../cache")
    DATA_PATH = Path("../data")
    MODEL_NAMES = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
                  'Logistic', 'SVM', 'KNN', 'NaiveBayes', 'NeuralNet']
    
    import logging
    logger = logging.getLogger("external_validation")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)
    
    def translate_term(term):
        return term
    
    def get_font_properties():
        """
        获取适合中文显示的字体属性
        
        Returns:
            FontProperties: 字体属性对象，如果找不到合适的中文字体则返回None
        """
        import platform
        try:
            from matplotlib.font_manager import FontProperties
            if platform.system() == 'Windows':
                try:
                    return FontProperties(fname=r"C:\Windows\Fonts\msyh.ttc")  # 微软雅黑
                except:
                    try:
                        return FontProperties(fname=r"C:\Windows\Fonts\simhei.ttf")  # 黑体
                    except:
                        return FontProperties(family=['Microsoft YaHei', 'SimHei', 'SimSun', 'sans-serif'])
            else:
                return FontProperties(family=['SimHei', 'DejaVu Sans', 'sans-serif'])
        except Exception as e:
            logger.warning(f"获取字体属性失败: {e}")
            return None

# 配置matplotlib
import matplotlib as mpl
# 添加更多中文字体支持，特别是微软雅黑和宋体（Windows系统常用）
mpl.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun', 'DejaVu Sans', 'Arial', 'Helvetica', 'sans-serif']
# 修复负号显示问题
mpl.rcParams['axes.unicode_minus'] = False
# 设置绘图风格
plt.style.use('seaborn-v0_8-whitegrid')

# 使用统一字体管理器
from font_manager import initialize_fonts, get_safe_title, get_safe_label

# 初始化字体
initialize_fonts()

# 获取全局字体属性
FONT_PROPERTIES = get_font_properties()

# 获取日志记录器
try:
    logger = get_default_logger("external_validation")
except:
    logger = logging.getLogger("external_validation")
    logger.setLevel(logging.INFO)
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)

def load_model(model_name):
    """
    加载已训练好的模型
    
    Args:
        model_name: 模型名称
        
    Returns:
        dict: 包含模型和相关信息的字典，如果加载失败则返回None
    """
    cache_file = CACHE_PATH / f"{model_name}_results.joblib"
    if not cache_file.exists():
        logger.error(f"缓存文件不存在: {cache_file}")
        return None
    
    try:
        data = load(cache_file)
        logger.info(f"成功加载模型 {model_name}")
        return data
    except Exception as e:
        logger.error(f"加载模型 {model_name} 失败: {e}")
        return None

def preprocess_external_data(data_file, model_name):
    """
    预处理外部验证数据集

    Args:
        data_file: 外部数据文件路径
        model_name: 模型名称，用于获取特征名称和预处理方法

    Returns:
        tuple: (X, y) 处理后的特征和标签
    """
    try:
        # 加载数据
        data, target_column, _ = validate_and_load_data(data_file, perform_validation=True)
        logger.info(f"成功加载外部验证数据: {data_file}")

        # 分离特征和标签
        if target_column not in data.columns:
            logger.error(f"目标列 '{target_column}' 不存在于数据中")
            return None, None

        X = data.drop(columns=[target_column])
        y = data[target_column]

        # 尝试加载特征名称和scaler
        try:
            feature_names_file = CACHE_PATH / f"{model_name}_feature_names.joblib"
            if feature_names_file.exists():
                feature_names = load(feature_names_file)
                logger.info(f"加载特征名称: {len(feature_names)} 个特征")

                # 检查外部数据集是否包含所有需要的特征
                missing_features = [f for f in feature_names if f not in X.columns]
                if missing_features:
                    logger.warning(f"外部数据集缺少以下特征: {missing_features}")
                    logger.warning("这可能导致模型性能下降或错误")

                # 只选择模型使用的特征，并按照相同的顺序排列
                X = X[feature_names]
                logger.info(f"已选择 {len(feature_names)} 个特征用于验证")
        except Exception as e:
            logger.warning(f"加载特征名称失败: {e}，将使用所有可用特征")

        return X, y
    except Exception as e:
        logger.error(f"预处理外部数据失败: {e}")
        return None, None

def evaluate_model_on_external_data(model_data, X, y):
    """
    在外部数据集上评估模型性能

    Args:
        model_data: 包含模型的字典
        X: 特征数据
        y: 标签数据

    Returns:
        dict: 包含评估结果的字典
    """
    model = model_data['model']

    # 应用训练时的scaler（如果存在）
    scaler = model_data.get('scaler', None)
    if scaler is not None:
        logger.info("应用训练时的数据缩放器到外部验证数据")
        try:
            # 确保X是DataFrame格式以保持列名
            if hasattr(X, 'columns'):
                X_scaled = pd.DataFrame(scaler.transform(X), columns=X.columns, index=X.index)
            else:
                X_scaled = scaler.transform(X)
            X = X_scaled
        except Exception as e:
            logger.warning(f"应用scaler失败: {e}，使用原始数据")
    else:
        logger.info("未找到训练时的scaler，使用原始数据进行预测")

    # 预测
    y_pred = model.predict(X)
    y_pred_proba = model.predict_proba(X)[:, 1] if hasattr(model, 'predict_proba') else y_pred
    
    # 计算评估指标
    accuracy = accuracy_score(y, y_pred)
    precision = precision_score(y, y_pred, zero_division=0)
    recall = recall_score(y, y_pred)
    f1 = f1_score(y, y_pred)
    
    # 计算ROC曲线和AUC
    fpr, tpr, _ = roc_curve(y, y_pred_proba)
    roc_auc = auc(fpr, tpr)
    
    # 计算混淆矩阵
    cm = confusion_matrix(y, y_pred)
    tn, fp, fn, tp = cm.ravel()
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    
    # 计算校准曲线
    prob_true, prob_pred = calibration_curve(y, y_pred_proba, n_bins=10)
    
    # 构建结果字典
    results = {
        'y_true': y,
        'y_pred': y_pred,
        'y_pred_proba': y_pred_proba,
        'accuracy': accuracy,
        'precision': precision,
        'recall': recall,
        'specificity': specificity,
        'f1_score': f1,
        'auc': roc_auc,
        'confusion_matrix': cm,
        'fpr': fpr,
        'tpr': tpr,
        'calibration': (prob_true, prob_pred),
        'X_test': X  # 用于SHAP分析
    }
    
    # 打印评估结果
    logger.info("外部验证结果:")
    logger.info(f"准确率: {accuracy:.4f}")
    logger.info(f"精确率: {precision:.4f}")
    logger.info(f"召回率: {recall:.4f}")
    logger.info(f"特异性: {specificity:.4f}")
    logger.info(f"F1分数: {f1:.4f}")
    logger.info(f"AUC: {roc_auc:.4f}")
    logger.info("混淆矩阵:")
    logger.info(f"{cm}")
    
    return results

def plot_external_validation_results(model_name, results):
    """
    绘制外部验证结果的可视化图表
    
    Args:
        model_name: 模型名称
        results: 评估结果字典
    """
    # 创建外部验证结果目录
    external_dir = OUTPUT_PATH / 'external_validation' / model_name
    external_dir.mkdir(parents=True, exist_ok=True)
    
    # 1. ROC曲线
    plt.figure(figsize=(10, 8))
    plt.plot(results['fpr'], results['tpr'], 
             label=f'{model_name} (AUC = {results["auc"]:.3f})', 
             color='blue', linewidth=2)
    plt.plot([0, 1], [0, 1], 'k--', linewidth=1)
    plt.xlabel('假阳性率 (FPR)', fontproperties=FONT_PROPERTIES)
    plt.ylabel('真阳性率 (TPR)', fontproperties=FONT_PROPERTIES)
    plt.title(f'{model_name} - 外部验证 ROC 曲线', fontproperties=FONT_PROPERTIES)
    if FONT_PROPERTIES:
        plt.legend(loc='lower right', prop=FONT_PROPERTIES)
    else:
        plt.legend(loc='lower right')
    plt.grid(True, alpha=0.3)
    plt.savefig(external_dir / f"{model_name}_external_roc.png", dpi=150, bbox_inches='tight')
    plt.close()
    
    # 2. 混淆矩阵
    plt.figure(figsize=(8, 6))
    sns.heatmap(results['confusion_matrix'], annot=True, fmt='d', cmap='Blues',
                xticklabels=['预测:阴性', '预测:阳性'],
                yticklabels=['实际:阴性', '实际:阳性'])
    plt.title(f'{model_name} - 外部验证混淆矩阵', fontproperties=FONT_PROPERTIES)
    plt.xlabel('预测类别', fontproperties=FONT_PROPERTIES)
    plt.ylabel('真实类别', fontproperties=FONT_PROPERTIES)
    plt.tight_layout()
    plt.savefig(external_dir / f"{model_name}_external_confusion_matrix.png", dpi=150, bbox_inches='tight')
    plt.close()
    
    # 3. 校准曲线
    plt.figure(figsize=(10, 8))
    plt.plot(results['calibration'][1], results['calibration'][0], 'o-', label=model_name)
    plt.plot([0, 1], [0, 1], 'k--', label='完美校准')
    plt.xlabel('预测概率', fontproperties=FONT_PROPERTIES)
    plt.ylabel('实际概率', fontproperties=FONT_PROPERTIES)
    plt.title(f'{model_name} - 外部验证校准曲线', fontproperties=FONT_PROPERTIES)
    if FONT_PROPERTIES:
        plt.legend(prop=FONT_PROPERTIES)
    else:
        plt.legend()
    plt.grid(True, alpha=0.3)
    plt.savefig(external_dir / f"{model_name}_external_calibration.png", dpi=150, bbox_inches='tight')
    plt.close()
    
    # 4. 性能指标雷达图
    categories = ['准确率', '精确率', '召回率', '特异性', 'F1分数', 'AUC']
    categories_values = [
        results['accuracy'], 
        results['precision'],
        results['recall'], 
        results['specificity'],
        results['f1_score'],
        results['auc']
    ]
    N = len(categories)
    
    angles = np.linspace(0, 2 * np.pi, N, endpoint=False).tolist()
    angles += angles[:1]
    
    # 使用更适合论文的配色方案
    plt.figure(figsize=(10, 10), facecolor='white')
    ax = plt.subplot(111, polar=True, facecolor='white')
    ax.set_theta_offset(np.pi / 2)
    ax.set_theta_direction(-1)
    ax.set_rlabel_position(0)
    
    # 设置网格样式
    ax.grid(True, color='lightgray', linestyle='-', linewidth=0.5, alpha=0.7)
    ax.set_ylim(0, 1)
    
    # 设置角度标签
    plt.xticks(angles[:-1], categories, fontsize=12, fontweight='bold', fontproperties=FONT_PROPERTIES)
    
    # 设置径向标签
    ax.set_yticks([0.2, 0.4, 0.6, 0.8, 1.0])
    ax.set_yticklabels(['0.2', '0.4', '0.6', '0.8', '1.0'], fontsize=10, alpha=0.7)
    
    # 确保values包含所有指标值并正确闭合
    values = categories_values.copy()
    values.append(values[0])  # 添加第一个值以闭合雷达图
    
    # 使用论文级别的配色：深蓝色线条，浅蓝色填充
    line_color = '#1f77b4'  # 深蓝色
    fill_color = '#aec7e8'  # 浅蓝色
    
    ax.plot(angles, values, linewidth=2.5, linestyle='solid', color=line_color, marker='o', markersize=8)
    ax.fill(angles, values, color=fill_color, alpha=0.25)
    
    plt.title(f'{model_name} - 外部验证性能雷达图', size=16, fontweight='bold', pad=20, fontproperties=FONT_PROPERTIES)
    plt.tight_layout()
    plt.savefig(external_dir / f"{model_name}_external_radar.png", dpi=150, bbox_inches='tight')
    plt.close()

def generate_external_validation_report(model_name, results, data_file):
    """
    生成外部验证报告
    
    Args:
        model_name: 模型名称
        results: 评估结果字典
        data_file: 外部数据文件路径
    """
    # 创建外部验证结果目录
    external_dir = OUTPUT_PATH / 'external_validation' / model_name
    external_dir.mkdir(parents=True, exist_ok=True)
    
    # 生成HTML报告
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>{model_name} - 外部验证报告</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2 {{ color: #333; }}
            table {{ border-collapse: collapse; width: 100%; margin-bottom: 20px; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            tr:nth-child(even) {{ background-color: #f9f9f9; }}
            .metrics {{ display: flex; flex-wrap: wrap; }}
            .metric-box {{ background-color: #f8f8f8; border-radius: 5px; padding: 15px; margin: 10px; flex: 1; min-width: 200px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
            .metric-value {{ font-size: 24px; font-weight: bold; color: #1f77b4; }}
            .images {{ display: flex; flex-wrap: wrap; justify-content: space-around; }}
            .image-container {{ margin: 10px; text-align: center; }}
            img {{ max-width: 100%; height: auto; border: 1px solid #ddd; border-radius: 4px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
        </style>
    </head>
    <body>
        <h1>{model_name} - 外部验证报告</h1>
        <p>外部验证数据集: {data_file}</p>
        <p>验证时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        
        <h2>性能指标摘要</h2>
        <div class="metrics">
            <div class="metric-box">
                <h3>准确率</h3>
                <div class="metric-value">{results['accuracy']:.4f}</div>
            </div>
            <div class="metric-box">
                <h3>精确率</h3>
                <div class="metric-value">{results['precision']:.4f}</div>
            </div>
            <div class="metric-box">
                <h3>召回率</h3>
                <div class="metric-value">{results['recall']:.4f}</div>
            </div>
            <div class="metric-box">
                <h3>特异性</h3>
                <div class="metric-value">{results['specificity']:.4f}</div>
            </div>
            <div class="metric-box">
                <h3>F1分数</h3>
                <div class="metric-value">{results['f1_score']:.4f}</div>
            </div>
            <div class="metric-box">
                <h3>AUC</h3>
                <div class="metric-value">{results['auc']:.4f}</div>
            </div>
        </div>
        
        <h2>混淆矩阵</h2>
        <table>
            <tr>
                <th></th>
                <th>预测:阴性</th>
                <th>预测:阳性</th>
            </tr>
            <tr>
                <th>实际:阴性</th>
                <td>{results['confusion_matrix'][0][0]}</td>
                <td>{results['confusion_matrix'][0][1]}</td>
            </tr>
            <tr>
                <th>实际:阳性</th>
                <td>{results['confusion_matrix'][1][0]}</td>
                <td>{results['confusion_matrix'][1][1]}</td>
            </tr>
        </table>
        
        <h2>可视化结果</h2>
        <div class="images">
            <div class="image-container">
                <img src="{model_name}_external_roc.png" alt="ROC曲线">
                <p>ROC曲线 (AUC = {results['auc']:.4f})</p>
            </div>
            <div class="image-container">
                <img src="{model_name}_external_confusion_matrix.png" alt="混淆矩阵">
                <p>混淆矩阵</p>
            </div>
            <div class="image-container">
                <img src="{model_name}_external_calibration.png" alt="校准曲线">
                <p>校准曲线</p>
            </div>
            <div class="image-container">
                <img src="{model_name}_external_radar.png" alt="性能雷达图">
                <p>性能雷达图</p>
            </div>
        </div>
        
        <h2>结论</h2>
        <p>
            该模型在外部验证集上的表现{
            '优秀' if results['auc'] > 0.9 else 
            '良好' if results['auc'] > 0.8 else 
            '一般' if results['auc'] > 0.7 else 
            '较差'
            }。
            AUC值为{results['auc']:.4f}，准确率为{results['accuracy']:.4f}。
        </p>
        <p>
            模型的精确率为{results['precision']:.4f}，召回率为{results['recall']:.4f}，
            特异性为{results['specificity']:.4f}，F1分数为{results['f1_score']:.4f}。
        </p>
        <p>
            根据混淆矩阵，模型在外部验证集上有{results['confusion_matrix'][0][0]}个真阴性，
            {results['confusion_matrix'][0][1]}个假阳性，{results['confusion_matrix'][1][0]}个假阴性，
            以及{results['confusion_matrix'][1][1]}个真阳性。
        </p>
    </body>
    </html>
    """
    
    # 保存HTML报告
    with open(external_dir / f"{model_name}_external_validation_report.html", 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    logger.info(f"外部验证报告已保存至: {external_dir / f'{model_name}_external_validation_report.html'}")

def run_external_validation(model_name, data_file):
    """
    运行外部验证流程
    
    Args:
        model_name: 模型名称
        data_file: 外部数据文件路径
        
    Returns:
        bool: 验证是否成功
    """
    # 1. 加载模型
    model_data = load_model(model_name)
    if model_data is None:
        return False
    
    # 2. 加载和预处理外部数据
    X, y = preprocess_external_data(data_file, model_name)
    if X is None or y is None:
        return False
    
    # 3. 评估模型
    results = evaluate_model_on_external_data(model_data, X, y)
    
    # 4. 绘制结果
    plot_external_validation_results(model_name, results)
    
    # 5. 生成报告
    generate_external_validation_report(model_name, results, data_file)
    
    # 6. 缓存外部验证结果
    try:
        cache_file = CACHE_PATH / f"{model_name}_external_validation_results.joblib"
        dump(results, cache_file)
        logger.info(f"外部验证结果已缓存至: {cache_file}")
    except Exception as e:
        logger.error(f"缓存外部验证结果失败: {e}")
    
    # 7. 尝试使用SHAP进行解释
    try:
        # 保存模型和结果以便plot_single_model可以使用
        external_results = {
            'model': model_data['model'],
            'X_test': X,
            'y_true': y,
            'y_pred': results['y_pred'],
            'y_pred_proba': results['y_pred_proba'],
            'feature_names': X.columns.tolist() if hasattr(X, 'columns') else None,
            'is_external_validation': True  # 标记这是外部验证数据
        }
        
        # 临时保存为模型结果，以便复用现有的绘图函数
        temp_cache_file = CACHE_PATH / f"{model_name}_results_external.joblib"
        dump(external_results, temp_cache_file)
        
        # 使用现有的绘图函数绘制SHAP图
        logger.info("尝试生成SHAP解释图...")
        
        # 导入plot_single_model模块中的可视化函数
        try:
            from plot_single_model import plot_shap_summary, plot_shap_dependence
            
            # 创建外部验证专用的SHAP输出目录
            shap_dir = OUTPUT_PATH / 'external_validation' / model_name / 'shap'
            shap_dir.mkdir(parents=True, exist_ok=True)
            
            # 生成SHAP摘要图
            logger.info("生成SHAP摘要图...")
            plot_shap_summary(
                model=model_data['model'],
                X=X,
                feature_names=X.columns.tolist() if hasattr(X, 'columns') else None,
                model_name=f"{model_name}_external",
                max_display=20,
                plot_type='bar',
                output_dir=shap_dir
            )
            
            # 生成SHAP依赖图（针对最重要的几个特征）
            logger.info("生成SHAP依赖图...")
            # 如果有特征名称，为最重要的几个特征生成依赖图
            if hasattr(X, 'columns') and len(X.columns) > 0:
                # 获取特征重要性（如果模型支持）
                feature_importance = None
                if hasattr(model_data['model'], 'feature_importances_'):
                    feature_importance = model_data['model'].feature_importances_
                
                # 如果有特征重要性，选择前5个最重要的特征
                if feature_importance is not None:
                    # 获取前5个最重要特征的索引
                    top_indices = feature_importance.argsort()[-5:][::-1]
                    top_features = [X.columns[i] for i in top_indices]
                    
                    # 为每个重要特征生成依赖图
                    for feature in top_features:
                        try:
                            plot_shap_dependence(
                                model=model_data['model'],
                                X=X,
                                feature=feature,
                                model_name=f"{model_name}_external",
                                output_dir=shap_dir
                            )
                        except Exception as e:
                            logger.warning(f"为特征 {feature} 生成SHAP依赖图失败: {e}")
            
            logger.info(f"SHAP解释图已保存至: {shap_dir}")
            
        except ImportError as e:
            logger.warning(f"导入SHAP绘图函数失败: {e}")
        except Exception as e:
            logger.warning(f"生成SHAP解释图失败: {e}")
    except Exception as e:
        logger.warning(f"生成SHAP解释图失败: {e}")
    
    return True

def generate_external_validation_report_for_all_models(selected_models, data_file):
    """
    生成所有模型的外部验证综合报告
    
    Args:
        selected_models: 要包含在报告中的模型列表
        data_file: 外部验证数据文件路径
    
    Returns:
        bool: 报告生成是否成功
    """
    logger.info("开始生成外部验证综合报告...")
    
    # 创建外部验证报告目录
    report_dir = OUTPUT_PATH / 'external_validation' / 'reports'
    report_dir.mkdir(parents=True, exist_ok=True)
    
    # 收集所有模型的验证结果
    all_results = {}
    all_metrics = {}
    
    for model_name in selected_models:
        # 尝试加载缓存的外部验证结果
        cache_file = CACHE_PATH / f"{model_name}_external_validation_results.joblib"
        if cache_file.exists():
            try:
                results = load(cache_file)
                logger.info(f"成功加载模型 {model_name} 的外部验证结果")
                all_results[model_name] = results
                
                # 提取性能指标
                metrics = {
                    'accuracy': results['accuracy'],
                    'precision': results['precision'],
                    'recall': results['recall'],
                    'specificity': results['specificity'],
                    'f1_score': results['f1_score'],
                    'auc': results['auc']
                }
                
                # 计算其他指标
                y_true = results['y_true']
                y_pred = results['y_pred']
                y_pred_proba = results['y_pred_proba']
                
                # 马修斯相关系数
                from sklearn.metrics import matthews_corrcoef
                metrics['mcc'] = matthews_corrcoef(y_true, y_pred)
                
                # Cohen's Kappa
                from sklearn.metrics import cohen_kappa_score
                metrics['kappa'] = cohen_kappa_score(y_true, y_pred)
                
                # 平衡准确率
                metrics['balanced_accuracy'] = (metrics['recall'] + metrics['specificity']) / 2
                
                # 精确率-召回率曲线下面积
                from sklearn.metrics import average_precision_score
                metrics['auc_pr'] = average_precision_score(y_true, y_pred_proba)
                
                all_metrics[model_name] = metrics
            except Exception as e:
                logger.error(f"加载模型 {model_name} 的外部验证结果失败: {e}")
        else:
            logger.warning(f"模型 {model_name} 的外部验证结果不存在，尝试运行验证")
            if run_external_validation(model_name, data_file):
                # 验证成功后重新尝试加载
                try:
                    results = load(cache_file)
                    logger.info(f"成功加载模型 {model_name} 的外部验证结果")
                    all_results[model_name] = results
                    
                    # 提取性能指标
                    metrics = {
                        'accuracy': results['accuracy'],
                        'precision': results['precision'],
                        'recall': results['recall'],
                        'specificity': results['specificity'],
                        'f1_score': results['f1_score'],
                        'auc': results['auc']
                    }
                    
                    # 计算其他指标
                    y_true = results['y_true']
                    y_pred = results['y_pred']
                    y_pred_proba = results['y_pred_proba']
                    
                    # 马修斯相关系数
                    from sklearn.metrics import matthews_corrcoef
                    metrics['mcc'] = matthews_corrcoef(y_true, y_pred)
                    
                    # Cohen's Kappa
                    from sklearn.metrics import cohen_kappa_score
                    metrics['kappa'] = cohen_kappa_score(y_true, y_pred)
                    
                    # 平衡准确率
                    metrics['balanced_accuracy'] = (metrics['recall'] + metrics['specificity']) / 2
                    
                    # 精确率-召回率曲线下面积
                    from sklearn.metrics import average_precision_score
                    metrics['auc_pr'] = average_precision_score(y_true, y_pred_proba)
                    
                    all_metrics[model_name] = metrics
                except Exception as e:
                    logger.error(f"加载模型 {model_name} 的外部验证结果失败: {e}")
    
    if not all_metrics:
        logger.error("没有可用的外部验证结果，无法生成报告")
        return False
    
    # 创建性能指标数据框
    df = pd.DataFrame(all_metrics).T
    
    # 计算综合得分
    # 使用主要指标的平均值作为综合得分
    main_metrics = ['accuracy', 'precision', 'recall', 'f1_score', 'specificity', 'auc']
    df['composite_score'] = df[main_metrics].mean(axis=1)
    
    # 按综合得分排序
    df_sorted = df.sort_values('composite_score', ascending=False)
    
    # 1. 创建性能热力图
    plt.figure(figsize=(14, 10))
    sns.heatmap(df[main_metrics].T, annot=True, cmap='RdYlBu_r', 
                center=0.5, fmt='.3f', cbar_kws={'label': '性能指标值'})
    plt.title('外部验证 - 模型性能指标热力图', fontsize=16, fontweight='bold', pad=20, fontproperties=FONT_PROPERTIES)
    plt.xlabel('模型名称', fontsize=12, fontproperties=FONT_PROPERTIES)
    plt.ylabel('性能指标', fontsize=12, fontproperties=FONT_PROPERTIES)
    plt.xticks(rotation=45)
    plt.yticks(rotation=0)
    plt.tight_layout()
    plt.savefig(report_dir / 'external_validation_heatmap.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    # 2. 创建雷达图
    plt.figure(figsize=(12, 10), facecolor='white')
    
    # 准备数据 - 确保使用所有主要指标
    categories = ['准确率', '精确率', '召回率', '特异性', 'F1分数', 'AUC']
    N = len(categories)
    angles = np.linspace(0, 2 * np.pi, N, endpoint=False).tolist()
    angles += angles[:1]  # 闭合雷达图
    
    # 设置雷达图参数
    ax = plt.subplot(111, polar=True, facecolor='white')
    ax.set_theta_offset(np.pi / 2)  # 从上方开始
    ax.set_theta_direction(-1)  # 顺时针
    ax.set_rlabel_position(0)
    
    # 绘制每个模型的雷达图
    colors = plt.cm.tab10(np.linspace(0, 1, len(df_sorted)))
    for i, (model_name, row) in enumerate(df_sorted.iterrows()):
        # 确保按照categories中的顺序获取值
        values = [
            row['accuracy'], 
            row['precision'], 
            row['recall'], 
            row['specificity'], 
            row['f1_score'], 
            row['auc']
        ]
        # 添加第一个值以闭合雷达图
        values += [values[0]]
        
        ax.plot(angles, values, linewidth=2, linestyle='solid', 
                label=model_name, color=colors[i])
        ax.fill(angles, values, alpha=0.1, color=colors[i])
    
    # 设置刻度和标签
    plt.xticks(angles[:-1], categories, fontsize=12, fontproperties=FONT_PROPERTIES)
    ax.set_ylim(0, 1)
    
    # 添加图例
    if FONT_PROPERTIES:
        plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1), prop=FONT_PROPERTIES)
    else:
        plt.legend(loc='upper right', bbox_to_anchor=(0.1, 0.1))
    
    plt.title('外部验证 - 模型性能雷达图', fontsize=16, fontweight='bold', pad=20, fontproperties=FONT_PROPERTIES)
    plt.tight_layout()
    plt.savefig(report_dir / 'external_validation_radar.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    # 3. 创建排名图
    plt.figure(figsize=(12, 8))
    ax = sns.barplot(x=df_sorted.index, y='composite_score', data=df_sorted, 
                    palette='viridis')
    
    # 在条形上方添加具体数值
    for i, v in enumerate(df_sorted['composite_score']):
        ax.text(i, v + 0.02, f'{v:.3f}', ha='center', fontsize=10)
    
    plt.title('外部验证 - 模型综合性能排名', fontsize=16, fontweight='bold', fontproperties=FONT_PROPERTIES)
    plt.xlabel('模型名称', fontsize=12, fontproperties=FONT_PROPERTIES)
    plt.ylabel('综合性能得分', fontsize=12, fontproperties=FONT_PROPERTIES)
    plt.ylim(0, 1.1)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(report_dir / 'external_validation_ranking.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    # 4. 生成HTML报告
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>外部验证 - 模型性能比较报告</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                color: #333;
                background-color: #f8f9fa;
            }}
            .container {{
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 30px;
                box-shadow: 0 0 10px rgba(0,0,0,0.1);
                border-radius: 5px;
            }}
            h1, h2 {{
                color: #2c3e50;
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
            }}
            h1 {{
                text-align: center;
                font-size: 28px;
                margin-bottom: 30px;
            }}
            h2 {{
                margin-top: 30px;
                font-size: 22px;
            }}
            table {{
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }}
            th, td {{
                padding: 12px;
                text-align: left;
                border-bottom: 1px solid #ddd;
            }}
            th {{
                background-color: #f2f2f2;
            }}
            tr:hover {{
                background-color: #f5f5f5;
            }}
            .summary {{
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 5px;
                margin-bottom: 30px;
            }}
            .best-score {{
                font-weight: bold;
                color: #27ae60;
            }}
            .images {{
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 20px;
                margin: 30px 0;
            }}
            .image-container {{
                max-width: 100%;
                text-align: center;
            }}
            img {{
                max-width: 100%;
                height: auto;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }}
            .metric-description {{
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 5px;
                margin-top: 30px;
            }}
            .footer {{
                text-align: center;
                font-size: 14px;
                color: #7f8c8d;
                margin-top: 10px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <h1>外部验证 - 模型性能比较报告</h1>
            <p style="text-align: center; color: #7f8c8d;">生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p style="text-align: center; color: #7f8c8d;">外部验证数据集: {data_file}</p>
            
            <div class="summary">
                <h2>📊 执行摘要</h2>
                <p><strong>最佳模型:</strong> {df_sorted.index[0]} (综合得分: {df_sorted.iloc[0]['composite_score']:.3f})</p>
                <p><strong>比较模型数量:</strong> {len(df)}</p>
                <p><strong>评估指标数量:</strong> {len(main_metrics)}</p>
            </div>
            
            <h2>🏆 模型排名</h2>
            <table>
                <tr>
                    <th>排名</th>
                    <th>模型名称</th>
                    <th>综合得分</th>
                    <th>准确率</th>
                    <th>精确率</th>
                    <th>召回率</th>
                    <th>特异性</th>
                    <th>F1分数</th>
                    <th>AUC</th>
                </tr>
    """
    
    # 添加排名表格
    for rank, (model_name, row) in enumerate(df_sorted.iterrows(), 1):
        html_content += f"""
                <tr>
                    <td>{rank}</td>
                    <td><strong>{model_name}</strong></td>
                    <td class="{'best-score' if rank == 1 else ''}">{row['composite_score']:.3f}</td>
                    <td>{row.get('accuracy', 0):.3f}</td>
                    <td>{row.get('precision', 0):.3f}</td>
                    <td>{row.get('recall', 0):.3f}</td>
                    <td>{row.get('specificity', 0):.3f}</td>
                    <td>{row.get('f1_score', 0):.3f}</td>
                    <td>{row.get('auc', 0):.3f}</td>
                </tr>
        """
    
    html_content += """
            </table>
            
            <h2>📈 可视化结果</h2>
            <div class="images">
                <div class="image-container">
                    <img src="external_validation_heatmap.png" alt="性能指标热力图">
                    <p>性能指标热力图</p>
                </div>
                <div class="image-container">
                    <img src="external_validation_radar.png" alt="性能雷达图">
                    <p>性能雷达图</p>
                </div>
                <div class="image-container">
                    <img src="external_validation_ranking.png" alt="模型排名">
                    <p>模型综合性能排名</p>
                </div>
            </div>
            
            <h2>📝 详细性能指标</h2>
            <table>
                <tr>
                    <th>模型</th>
    """
    
    # 英文列名到中文的映射
    column_mapping = {
        'accuracy': '准确率',
        'precision': '精确率', 
        'recall': '召回率',
        'f1_score': 'F1分数',
        'specificity': '特异性',
        'auc': 'AUC',
        'mcc': 'MCC',
        'kappa': 'Kappa',
        'balanced_accuracy': '平衡准确率',
        'auc_pr': 'AUC-PR'
    }
    
    # 添加指标列标题
    for col in df.columns:
        if col != 'composite_score':
            chinese_name = column_mapping.get(col, col)
            html_content += f"<th>{chinese_name}</th>"
    
    html_content += "</tr>"
    
    # 添加详细指标数据
    for model_name, row in df.iterrows():
        html_content += f"<tr><td><strong>{model_name}</strong></td>"
        for col in df.columns:
            if col != 'composite_score':
                value = row[col]
                # 高亮最佳值
                is_best = value == df[col].max()
                cell_class = 'best-score' if is_best else ''
                html_content += f'<td class="{cell_class}">{value:.3f}</td>'
        html_content += "</tr>"
    
    html_content += f"""
            </table>
            
            <div class="metric-description">
                <h2>📝 指标说明</h2>
                <ul>
                    <li><strong>准确率(Accuracy):</strong> 正确预测实例的比例</li>
                    <li><strong>精确率(Precision):</strong> 预测为正例中实际为正例的比例</li>
                    <li><strong>召回率(Recall):</strong> 实际正例中被正确预测的比例</li>
                    <li><strong>F1分数(F1 Score):</strong> 精确率和召回率的调和平均数</li>
                    <li><strong>特异性(Specificity):</strong> 实际负例中被正确预测的比例</li>
                    <li><strong>AUC:</strong> ROC曲线下面积，衡量分类器性能</li>
                    <li><strong>AUC-PR:</strong> 精确率-召回率曲线下面积</li>
                    <li><strong>MCC:</strong> 马修斯相关系数，平衡的分类性能度量</li>
                    <li><strong>Kappa:</strong> 科恩卡帕系数，考虑随机一致性</li>
                    <li><strong>平衡准确率(Balanced Accuracy):</strong> 敏感性和特异性的平均值</li>
                </ul>
            </div>
            
            <div class="summary">
                <h2>💡 推荐建议</h2>
                <p>基于外部验证数据集的综合性能评估，我们推荐使用 <strong>{df_sorted.index[0]}</strong> 模型进行后续任务。</p>
                <p>如果您对特定指标有具体要求，请参考详细性能指标表格选择最合适的模型。</p>
            </div>
            
            <div class="footer">
                <p>外部验证综合报告 | 生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # 保存HTML报告
    with open(report_dir / 'external_validation_report.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    # 5. 保存JSON数据
    performance_data = {
        'generation_time': pd.Timestamp.now().isoformat(),
        'external_data_file': str(data_file),
        'best_model': df_sorted.index[0],
        'best_score': float(df_sorted.iloc[0]['composite_score']),
        'model_count': len(df),
        'detailed_metrics': df.to_dict('index'),
        'ranking': [(model, float(score)) for model, score in 
                   zip(df_sorted.index, df_sorted['composite_score'])]
    }
    
    with open(report_dir / 'external_validation_data.json', 'w', encoding='utf-8') as f:
        json.dump(performance_data, f, ensure_ascii=False, indent=2)
    
    # 6. 保存CSV格式的详细数据
    df.to_csv(report_dir / 'external_validation_metrics.csv', encoding='utf-8-sig')
    
    logger.info(f"\n📊 外部验证综合报告生成完成!")
    logger.info(f"📁 报告文件位置: {report_dir}")
    logger.info(f"🏆 最佳模型: {df_sorted.index[0]} (得分: {df_sorted.iloc[0]['composite_score']:.3f})")
    logger.info(f"📄 查看详细报告: {report_dir / 'external_validation_report.html'}")
    
    return True

def main():
    # 创建参数解析器
    parser = argparse.ArgumentParser(description="在外部数据集上验证模型")
    parser.add_argument("--model", type=str, required=True,
                       help="要验证的模型名称，使用'All'验证所有模型，或用逗号分隔的模型列表")
    parser.add_argument("--data", type=str, required=True,
                       help="外部验证数据集文件路径 (CSV格式)")
    parser.add_argument("--report", action="store_true",
                       help="是否生成综合报告")
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    (OUTPUT_PATH / 'external_validation').mkdir(parents=True, exist_ok=True)
    
    # 处理模型参数
    if args.model == "All":
        selected_models = MODEL_NAMES
    else:
        selected_models = [model.strip() for model in args.model.split(',')]
        invalid_models = [model for model in selected_models if model not in MODEL_NAMES]
        if invalid_models:
            logger.error(f"错误: 以下模型名称无效: {', '.join(invalid_models)}")
            logger.info(f"支持的模型: {', '.join(MODEL_NAMES)}")
            return False
    
    # 如果只需要生成报告，不运行验证
    if args.report:
        logger.info("生成外部验证综合报告")
        return generate_external_validation_report_for_all_models(selected_models, args.data)
    
    # 运行外部验证
    success_count = 0
    for model_name in selected_models:
        logger.info(f"开始在外部数据集上验证模型: {model_name}")
        if run_external_validation(model_name, args.data):
            logger.info(f"模型 {model_name} 的外部验证完成")
            success_count += 1
        else:
            logger.error(f"模型 {model_name} 的外部验证失败")
    
    logger.info(f"外部验证完成，成功验证 {success_count}/{len(selected_models)} 个模型")
    
    # 如果验证了多个模型，自动生成综合报告
    if len(selected_models) > 1:
        logger.info("生成外部验证综合报告")
        generate_external_validation_report_for_all_models(selected_models, args.data)
    
    return success_count == len(selected_models)

if __name__ == "__main__":
    main()
