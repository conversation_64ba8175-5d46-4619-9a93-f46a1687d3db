2025-08-08 18:38:50 - model_training - INFO - 模型名称: Random Forest
2025-08-08 18:38:50 - model_training - INFO - 准确率: 0.7000
2025-08-08 18:38:50 - model_training - INFO - AUC: 0.8313
2025-08-08 18:38:50 - model_training - INFO - AUPRC: 0.8569
2025-08-08 18:38:50 - model_training - INFO - 混淆矩阵:
2025-08-08 18:38:50 - model_training - INFO - 
[[13  7]
 [ 5 15]]
2025-08-08 18:38:50 - model_training - INFO - 
分类报告:
2025-08-08 18:38:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.72      0.65      0.68        20
           1       0.68      0.75      0.71        20

    accuracy                           0.70        40
   macro avg       0.70      0.70      0.70        40
weighted avg       0.70      0.70      0.70        40

2025-08-08 18:38:50 - model_training - INFO - 训练时间: 0.25 秒
2025-08-08 18:38:50 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7000
2025-08-08 18:38:50 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 18:38:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型名称: Random Forest
2025-08-08 18:45:41 - model_training - INFO - 准确率: 0.8750
2025-08-08 18:45:41 - model_training - INFO - AUC: 0.9412
2025-08-08 18:45:41 - model_training - INFO - AUPRC: 0.9359
2025-08-08 18:45:41 - model_training - INFO - 混淆矩阵:
2025-08-08 18:45:41 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 18:45:41 - model_training - INFO - 
分类报告:
2025-08-08 18:45:41 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 18:45:41 - model_training - INFO - 训练时间: 0.09 秒
2025-08-08 18:45:41 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 18:45:41 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_184541\models\RandomForest_single_184541.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型名称: SVM
2025-08-08 18:45:41 - model_training - INFO - 准确率: 0.7750
2025-08-08 18:45:41 - model_training - INFO - AUC: 0.9182
2025-08-08 18:45:41 - model_training - INFO - AUPRC: 0.9034
2025-08-08 18:45:41 - model_training - INFO - 混淆矩阵:
2025-08-08 18:45:41 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-08 18:45:41 - model_training - INFO - 
分类报告:
2025-08-08 18:45:41 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-08 18:45:41 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 18:45:41 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-08 18:45:41 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_184541\models\SVM_single_184541.joblib
2025-08-08 18:45:41 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 19:01:34 - model_training - INFO - 模型名称: Random Forest
2025-08-08 19:01:34 - model_training - INFO - 准确率: 0.8750
2025-08-08 19:01:34 - model_training - INFO - AUC: 0.9412
2025-08-08 19:01:34 - model_training - INFO - AUPRC: 0.9359
2025-08-08 19:01:34 - model_training - INFO - 混淆矩阵:
2025-08-08 19:01:34 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 19:01:34 - model_training - INFO - 
分类报告:
2025-08-08 19:01:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 19:01:34 - model_training - INFO - 训练时间: 0.09 秒
2025-08-08 19:01:34 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 19:01:34 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_190134\models\RandomForest_single_190134.joblib
2025-08-08 19:01:34 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 20:16:53 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 20:16:53 - model_training - INFO - 准确率: 0.5500
2025-08-08 20:16:53 - model_training - INFO - AUC: 0.6125
2025-08-08 20:16:53 - model_training - INFO - AUPRC: 0.4726
2025-08-08 20:16:53 - model_training - INFO - 混淆矩阵:
2025-08-08 20:16:53 - model_training - INFO - 
[[12 15]
 [ 3 10]]
2025-08-08 20:16:53 - model_training - INFO - 
分类报告:
2025-08-08 20:16:53 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.44      0.57        27
           1       0.40      0.77      0.53        13

    accuracy                           0.55        40
   macro avg       0.60      0.61      0.55        40
weighted avg       0.67      0.55      0.56        40

2025-08-08 20:16:53 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 20:16:53 - model_training - INFO - 模型 Logistic 性能: 准确率=0.5500
2025-08-08 20:16:53 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 20:16:53 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-08 20:17:21 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 20:17:21 - model_training - INFO - 准确率: 0.5500
2025-08-08 20:17:21 - model_training - INFO - AUC: 0.6125
2025-08-08 20:17:21 - model_training - INFO - AUPRC: 0.4726
2025-08-08 20:17:21 - model_training - INFO - 混淆矩阵:
2025-08-08 20:17:21 - model_training - INFO - 
[[12 15]
 [ 3 10]]
2025-08-08 20:17:21 - model_training - INFO - 
分类报告:
2025-08-08 20:17:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.44      0.57        27
           1       0.40      0.77      0.53        13

    accuracy                           0.55        40
   macro avg       0.60      0.61      0.55        40
weighted avg       0.67      0.55      0.56        40

2025-08-08 20:17:21 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 20:17:21 - model_training - INFO - 模型 Logistic 性能: 准确率=0.5500
2025-08-08 20:17:21 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 20:17:21 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-08 21:19:24 - model_training - INFO - 模型名称: Random Forest
2025-08-08 21:19:24 - model_training - INFO - 准确率: 0.8780
2025-08-08 21:19:24 - model_training - INFO - AUC: 0.9481
2025-08-08 21:19:24 - model_training - INFO - AUPRC: 0.9453
2025-08-08 21:19:24 - model_training - INFO - 混淆矩阵:
2025-08-08 21:19:24 - model_training - INFO - 
[[21  2]
 [ 3 15]]
2025-08-08 21:19:24 - model_training - INFO - 
分类报告:
2025-08-08 21:19:24 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.83      0.86        18

    accuracy                           0.88        41
   macro avg       0.88      0.87      0.88        41
weighted avg       0.88      0.88      0.88        41

2025-08-08 21:19:24 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8780
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_211924\models\RandomForest_single_211924.joblib
2025-08-08 21:19:24 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 21:27:50 - model_training - INFO - 模型名称: Random Forest
2025-08-08 21:27:50 - model_training - INFO - 准确率: 0.7000
2025-08-08 21:27:50 - model_training - INFO - AUC: 0.8313
2025-08-08 21:27:50 - model_training - INFO - AUPRC: 0.8569
2025-08-08 21:27:50 - model_training - INFO - 混淆矩阵:
2025-08-08 21:27:50 - model_training - INFO - 
[[13  7]
 [ 5 15]]
2025-08-08 21:27:50 - model_training - INFO - 
分类报告:
2025-08-08 21:27:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.72      0.65      0.68        20
           1       0.68      0.75      0.71        20

    accuracy                           0.70        40
   macro avg       0.70      0.70      0.70        40
weighted avg       0.70      0.70      0.70        40

2025-08-08 21:27:50 - model_training - INFO - 训练时间: 0.25 秒
2025-08-08 21:27:50 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7000
2025-08-08 21:27:50 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 21:27:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-08 21:57:18 - model_training - INFO - 模型名称: Random Forest
2025-08-08 21:57:18 - model_training - INFO - 准确率: 0.8750
2025-08-08 21:57:18 - model_training - INFO - AUC: 0.9412
2025-08-08 21:57:18 - model_training - INFO - AUPRC: 0.9359
2025-08-08 21:57:18 - model_training - INFO - 混淆矩阵:
2025-08-08 21:57:18 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 21:57:18 - model_training - INFO - 
分类报告:
2025-08-08 21:57:18 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 21:57:18 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 21:57:18 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 21:57:18 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_215718\models\RandomForest_single_215718.joblib
2025-08-08 21:57:18 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型名称: Decision Tree
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.8000
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.8951
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.7948
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-08 23:09:19 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\DecisionTree_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型名称: Random Forest
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.9412
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.9359
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.07 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 23:09:19 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\RandomForest_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 23:09:19 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:09:19 - model_training - INFO - 模型名称: XGBoost
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.9000
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.9719
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.9627
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.05 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-08 23:09:19 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\XGBoost_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-08 23:09:19 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:09:19 - model_training - INFO - 模型名称: LightGBM
2025-08-08 23:09:19 - model_training - INFO - 准确率: 0.8500
2025-08-08 23:09:19 - model_training - INFO - AUC: 0.9488
2025-08-08 23:09:19 - model_training - INFO - AUPRC: 0.9492
2025-08-08 23:09:19 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:19 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-08 23:09:19 - model_training - INFO - 
分类报告:
2025-08-08 23:09:19 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-08 23:09:19 - model_training - INFO - 训练时间: 0.03 秒
2025-08-08 23:09:19 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-08 23:09:19 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\LightGBM_single_230919.joblib
2025-08-08 23:09:19 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-08 23:09:19 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-08 23:09:20 - model_training - INFO - 模型名称: CatBoost
2025-08-08 23:09:20 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:20 - model_training - INFO - AUC: 0.9591
2025-08-08 23:09:20 - model_training - INFO - AUPRC: 0.9570
2025-08-08 23:09:20 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:20 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:20 - model_training - INFO - 
分类报告:
2025-08-08 23:09:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:20 - model_training - INFO - 训练时间: 1.02 秒
2025-08-08 23:09:20 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-08 23:09:20 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\CatBoost_single_230920.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 23:09:20 - model_training - INFO - 准确率: 0.8250
2025-08-08 23:09:20 - model_training - INFO - AUC: 0.9284
2025-08-08 23:09:20 - model_training - INFO - AUPRC: 0.9288
2025-08-08 23:09:20 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:20 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-08 23:09:20 - model_training - INFO - 
分类报告:
2025-08-08 23:09:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-08 23:09:20 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:09:20 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-08 23:09:20 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\Logistic_single_230920.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 23:09:20 - model_training - INFO - 模型名称: SVM
2025-08-08 23:09:20 - model_training - INFO - 准确率: 0.7750
2025-08-08 23:09:20 - model_training - INFO - AUC: 0.9182
2025-08-08 23:09:20 - model_training - INFO - AUPRC: 0.9034
2025-08-08 23:09:20 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:20 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-08 23:09:20 - model_training - INFO - 
分类报告:
2025-08-08 23:09:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-08 23:09:20 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:09:20 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-08 23:09:20 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\SVM_single_230920.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型名称: KNN
2025-08-08 23:09:21 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:21 - model_training - INFO - AUC: 0.9322
2025-08-08 23:09:21 - model_training - INFO - AUPRC: 0.9189
2025-08-08 23:09:21 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:21 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:21 - model_training - INFO - 
分类报告:
2025-08-08 23:09:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:21 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:09:21 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-08 23:09:21 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\KNN_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型名称: Naive Bayes
2025-08-08 23:09:21 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:21 - model_training - INFO - AUC: 0.8977
2025-08-08 23:09:21 - model_training - INFO - AUPRC: 0.9096
2025-08-08 23:09:21 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:21 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-08 23:09:21 - model_training - INFO - 
分类报告:
2025-08-08 23:09:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:21 - model_training - INFO - 训练时间: 0.00 秒
2025-08-08 23:09:21 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-08 23:09:21 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NaiveBayes_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型名称: Neural Network
2025-08-08 23:09:21 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:09:21 - model_training - INFO - AUC: 0.9591
2025-08-08 23:09:21 - model_training - INFO - AUPRC: 0.9450
2025-08-08 23:09:21 - model_training - INFO - 混淆矩阵:
2025-08-08 23:09:21 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:09:21 - model_training - INFO - 
分类报告:
2025-08-08 23:09:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:09:21 - model_training - INFO - 训练时间: 0.24 秒
2025-08-08 23:09:21 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-08 23:09:21 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NeuralNet_single_230921.joblib
2025-08-08 23:09:21 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型名称: Decision Tree
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.8000
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.8951
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.7948
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.00 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-08 23:12:11 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\DecisionTree_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型名称: Random Forest
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.9412
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.9359
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-08 23:12:11 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\RandomForest_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 23:12:11 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:12:11 - model_training - INFO - 模型名称: XGBoost
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.9000
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.9719
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.9627
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.04 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-08 23:12:11 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\XGBoost_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-08 23:12:11 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:12:11 - model_training - INFO - 模型名称: LightGBM
2025-08-08 23:12:11 - model_training - INFO - 准确率: 0.8500
2025-08-08 23:12:11 - model_training - INFO - AUC: 0.9488
2025-08-08 23:12:11 - model_training - INFO - AUPRC: 0.9492
2025-08-08 23:12:11 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:11 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-08 23:12:11 - model_training - INFO - 
分类报告:
2025-08-08 23:12:11 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-08 23:12:11 - model_training - INFO - 训练时间: 0.04 秒
2025-08-08 23:12:11 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-08 23:12:11 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\LightGBM_single_231211.joblib
2025-08-08 23:12:11 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-08 23:12:11 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-08 23:12:12 - model_training - INFO - 模型名称: CatBoost
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9591
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9570
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 1.04 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-08 23:12:12 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\CatBoost_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8250
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9284
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9288
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-08 23:12:12 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\Logistic_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型名称: SVM
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.7750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9182
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9034
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-08 23:12:12 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\SVM_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型名称: KNN
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.9322
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9189
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.02 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-08 23:12:12 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\KNN_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型名称: Naive Bayes
2025-08-08 23:12:12 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:12 - model_training - INFO - AUC: 0.8977
2025-08-08 23:12:12 - model_training - INFO - AUPRC: 0.9096
2025-08-08 23:12:12 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:12 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-08 23:12:12 - model_training - INFO - 
分类报告:
2025-08-08 23:12:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:12:12 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-08 23:12:12 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NaiveBayes_single_231212.joblib
2025-08-08 23:12:12 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-08 23:12:13 - model_training - INFO - 模型名称: Neural Network
2025-08-08 23:12:13 - model_training - INFO - 准确率: 0.8750
2025-08-08 23:12:13 - model_training - INFO - AUC: 0.9591
2025-08-08 23:12:13 - model_training - INFO - AUPRC: 0.9450
2025-08-08 23:12:13 - model_training - INFO - 混淆矩阵:
2025-08-08 23:12:13 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-08 23:12:13 - model_training - INFO - 
分类报告:
2025-08-08 23:12:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-08 23:12:13 - model_training - INFO - 训练时间: 0.24 秒
2025-08-08 23:12:13 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-08 23:12:13 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_230919\models\NeuralNet_single_231213.joblib
2025-08-08 23:12:13 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型名称: Decision Tree
2025-08-08 23:51:26 - model_training - INFO - 准确率: 0.7500
2025-08-08 23:51:26 - model_training - INFO - AUC: 0.8465
2025-08-08 23:51:26 - model_training - INFO - AUPRC: 0.8245
2025-08-08 23:51:26 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:26 - model_training - INFO - 
[[17  6]
 [ 4 13]]
2025-08-08 23:51:26 - model_training - INFO - 
分类报告:
2025-08-08 23:51:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.74      0.77        23
           1       0.68      0.76      0.72        17

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.76      0.75      0.75        40

2025-08-08 23:51:26 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:51:26 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7500
2025-08-08 23:51:26 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\DecisionTree_single_235126.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型名称: Random Forest
2025-08-08 23:51:26 - model_training - INFO - 准确率: 0.6500
2025-08-08 23:51:26 - model_training - INFO - AUC: 0.8031
2025-08-08 23:51:26 - model_training - INFO - AUPRC: 0.7200
2025-08-08 23:51:26 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:26 - model_training - INFO - 
[[19  4]
 [10  7]]
2025-08-08 23:51:26 - model_training - INFO - 
分类报告:
2025-08-08 23:51:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.66      0.83      0.73        23
           1       0.64      0.41      0.50        17

    accuracy                           0.65        40
   macro avg       0.65      0.62      0.62        40
weighted avg       0.65      0.65      0.63        40

2025-08-08 23:51:26 - model_training - INFO - 训练时间: 0.08 秒
2025-08-08 23:51:26 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.6500
2025-08-08 23:51:26 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\RandomForest_single_235126.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-08 23:51:26 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:51:26 - model_training - INFO - 模型名称: XGBoost
2025-08-08 23:51:26 - model_training - INFO - 准确率: 0.7500
2025-08-08 23:51:26 - model_training - INFO - AUC: 0.8261
2025-08-08 23:51:26 - model_training - INFO - AUPRC: 0.7850
2025-08-08 23:51:26 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:26 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-08 23:51:26 - model_training - INFO - 
分类报告:
2025-08-08 23:51:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-08 23:51:26 - model_training - INFO - 训练时间: 0.04 秒
2025-08-08 23:51:26 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.7500
2025-08-08 23:51:26 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\XGBoost_single_235126.joblib
2025-08-08 23:51:26 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-08 23:51:26 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-08 23:51:26 - model_training - INFO - 模型名称: LightGBM
2025-08-08 23:51:26 - model_training - INFO - 准确率: 0.7500
2025-08-08 23:51:26 - model_training - INFO - AUC: 0.8517
2025-08-08 23:51:26 - model_training - INFO - AUPRC: 0.8381
2025-08-08 23:51:26 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:26 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-08 23:51:26 - model_training - INFO - 
分类报告:
2025-08-08 23:51:26 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-08 23:51:26 - model_training - INFO - 训练时间: 0.04 秒
2025-08-08 23:51:27 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.7500
2025-08-08 23:51:27 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\LightGBM_single_235127.joblib
2025-08-08 23:51:27 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-08 23:51:27 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-08 23:51:28 - model_training - INFO - 模型名称: CatBoost
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.7500
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.8593
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.8532
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 1.02 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.7500
2025-08-08 23:51:28 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\CatBoost_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型名称: Logistic Regression
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.7750
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.8159
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.8053
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[21  2]
 [ 7 10]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.75      0.91      0.82        23
           1       0.83      0.59      0.69        17

    accuracy                           0.78        40
   macro avg       0.79      0.75      0.76        40
weighted avg       0.79      0.78      0.77        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-08 23:51:28 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\Logistic_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型名称: SVM
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.6750
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.7980
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.7143
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[22  1]
 [12  5]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.96      0.77        23
           1       0.83      0.29      0.43        17

    accuracy                           0.68        40
   macro avg       0.74      0.63      0.60        40
weighted avg       0.73      0.68      0.63        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 SVM 性能: 准确率=0.6750
2025-08-08 23:51:28 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\SVM_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型名称: KNN
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.6250
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.7647
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.6474
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[17  6]
 [ 9  8]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.74      0.69        23
           1       0.57      0.47      0.52        17

    accuracy                           0.62        40
   macro avg       0.61      0.60      0.61        40
weighted avg       0.62      0.62      0.62        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 KNN 性能: 准确率=0.6250
2025-08-08 23:51:28 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\KNN_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型名称: Naive Bayes
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.6500
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.7724
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.7442
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[11 12]
 [ 2 15]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.48      0.61        23
           1       0.56      0.88      0.68        17

    accuracy                           0.65        40
   macro avg       0.70      0.68      0.65        40
weighted avg       0.72      0.65      0.64        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 0.01 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.6500
2025-08-08 23:51:28 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\NaiveBayes_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型名称: Neural Network
2025-08-08 23:51:28 - model_training - INFO - 准确率: 0.8000
2025-08-08 23:51:28 - model_training - INFO - AUC: 0.8261
2025-08-08 23:51:28 - model_training - INFO - AUPRC: 0.7876
2025-08-08 23:51:28 - model_training - INFO - 混淆矩阵:
2025-08-08 23:51:28 - model_training - INFO - 
[[19  4]
 [ 4 13]]
2025-08-08 23:51:28 - model_training - INFO - 
分类报告:
2025-08-08 23:51:28 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.83      0.83        23
           1       0.76      0.76      0.76        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.80      0.80      0.80        40

2025-08-08 23:51:28 - model_training - INFO - 训练时间: 0.56 秒
2025-08-08 23:51:28 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8000
2025-08-08 23:51:28 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250808_235126\models\NeuralNet_single_235128.joblib
2025-08-08 23:51:28 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:09:34 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:09:34 - model_training - INFO - AUC: 0.8465
2025-08-09 00:09:34 - model_training - INFO - AUPRC: 0.8245
2025-08-09 00:09:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:34 - model_training - INFO - 
[[17  6]
 [ 4 13]]
2025-08-09 00:09:34 - model_training - INFO - 
分类报告:
2025-08-09 00:09:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.74      0.77        23
           1       0.68      0.76      0.72        17

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.76      0.75      0.75        40

2025-08-09 00:09:34 - model_training - INFO - 训练时间: 0.00 秒
2025-08-09 00:09:34 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7500
2025-08-09 00:09:34 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\DecisionTree_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:09:34 - model_training - INFO - 准确率: 0.6500
2025-08-09 00:09:34 - model_training - INFO - AUC: 0.8031
2025-08-09 00:09:34 - model_training - INFO - AUPRC: 0.7200
2025-08-09 00:09:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:34 - model_training - INFO - 
[[19  4]
 [10  7]]
2025-08-09 00:09:34 - model_training - INFO - 
分类报告:
2025-08-09 00:09:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.66      0.83      0.73        23
           1       0.64      0.41      0.50        17

    accuracy                           0.65        40
   macro avg       0.65      0.62      0.62        40
weighted avg       0.65      0.65      0.63        40

2025-08-09 00:09:34 - model_training - INFO - 训练时间: 0.10 秒
2025-08-09 00:09:34 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.6500
2025-08-09 00:09:34 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\RandomForest_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:09:34 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:09:34 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:09:34 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:09:34 - model_training - INFO - AUC: 0.8261
2025-08-09 00:09:34 - model_training - INFO - AUPRC: 0.7850
2025-08-09 00:09:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:34 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-09 00:09:34 - model_training - INFO - 
分类报告:
2025-08-09 00:09:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-09 00:09:34 - model_training - INFO - 训练时间: 0.05 秒
2025-08-09 00:09:34 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.7500
2025-08-09 00:09:34 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\XGBoost_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:09:34 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:09:34 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:09:34 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:09:34 - model_training - INFO - AUC: 0.8517
2025-08-09 00:09:34 - model_training - INFO - AUPRC: 0.8381
2025-08-09 00:09:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:34 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-09 00:09:34 - model_training - INFO - 
分类报告:
2025-08-09 00:09:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-09 00:09:34 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:09:34 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.7500
2025-08-09 00:09:34 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\LightGBM_single_000934.joblib
2025-08-09 00:09:34 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:09:34 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:09:36 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.8593
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.8532
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[18  5]
 [ 5 12]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.78      0.78      0.78        23
           1       0.71      0.71      0.71        17

    accuracy                           0.75        40
   macro avg       0.74      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 1.08 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.7500
2025-08-09 00:09:36 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\CatBoost_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.8159
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.8053
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[21  2]
 [ 7 10]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.75      0.91      0.82        23
           1       0.83      0.59      0.69        17

    accuracy                           0.78        40
   macro avg       0.79      0.75      0.76        40
weighted avg       0.79      0.78      0.77        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-09 00:09:36 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\Logistic_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型名称: SVM
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.6750
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.7980
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.7143
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[22  1]
 [12  5]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.96      0.77        23
           1       0.83      0.29      0.43        17

    accuracy                           0.68        40
   macro avg       0.74      0.63      0.60        40
weighted avg       0.73      0.68      0.63        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 SVM 性能: 准确率=0.6750
2025-08-09 00:09:36 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\SVM_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型名称: KNN
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.6250
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.7647
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.6474
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[17  6]
 [ 9  8]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.65      0.74      0.69        23
           1       0.57      0.47      0.52        17

    accuracy                           0.62        40
   macro avg       0.61      0.60      0.61        40
weighted avg       0.62      0.62      0.62        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 KNN 性能: 准确率=0.6250
2025-08-09 00:09:36 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\KNN_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.6500
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.7724
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.7442
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[11 12]
 [ 2 15]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.48      0.61        23
           1       0.56      0.88      0.68        17

    accuracy                           0.65        40
   macro avg       0.70      0.68      0.65        40
weighted avg       0.72      0.65      0.64        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.6500
2025-08-09 00:09:36 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\NaiveBayes_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:09:36 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:09:36 - model_training - INFO - AUC: 0.8261
2025-08-09 00:09:36 - model_training - INFO - AUPRC: 0.7876
2025-08-09 00:09:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:09:36 - model_training - INFO - 
[[19  4]
 [ 4 13]]
2025-08-09 00:09:36 - model_training - INFO - 
分类报告:
2025-08-09 00:09:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.83      0.83        23
           1       0.76      0.76      0.76        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.80      0.80      0.80        40

2025-08-09 00:09:36 - model_training - INFO - 训练时间: 0.56 秒
2025-08-09 00:09:36 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8000
2025-08-09 00:09:36 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_000934\models\NeuralNet_single_000936.joblib
2025-08-09 00:09:36 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:16:54 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:16:54 - model_training - INFO - AUC: 0.8951
2025-08-09 00:16:54 - model_training - INFO - AUPRC: 0.7948
2025-08-09 00:16:54 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:54 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-09 00:16:54 - model_training - INFO - 
分类报告:
2025-08-09 00:16:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-09 00:16:54 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:16:54 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-09 00:16:54 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\DecisionTree_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:16:54 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:16:54 - model_training - INFO - AUC: 0.9412
2025-08-09 00:16:54 - model_training - INFO - AUPRC: 0.9359
2025-08-09 00:16:54 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:54 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 00:16:54 - model_training - INFO - 
分类报告:
2025-08-09 00:16:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:16:54 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 00:16:54 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 00:16:54 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\RandomForest_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:16:54 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:16:54 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:16:54 - model_training - INFO - 准确率: 0.9000
2025-08-09 00:16:54 - model_training - INFO - AUC: 0.9719
2025-08-09 00:16:54 - model_training - INFO - AUPRC: 0.9627
2025-08-09 00:16:54 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:54 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-09 00:16:54 - model_training - INFO - 
分类报告:
2025-08-09 00:16:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-09 00:16:54 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:16:54 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-09 00:16:54 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\XGBoost_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:16:54 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:16:54 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:16:54 - model_training - INFO - 准确率: 0.8500
2025-08-09 00:16:54 - model_training - INFO - AUC: 0.9488
2025-08-09 00:16:54 - model_training - INFO - AUPRC: 0.9492
2025-08-09 00:16:54 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:54 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-09 00:16:54 - model_training - INFO - 
分类报告:
2025-08-09 00:16:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-09 00:16:54 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 00:16:54 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-09 00:16:54 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\LightGBM_single_001654.joblib
2025-08-09 00:16:54 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:16:54 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:16:55 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:16:55 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:16:55 - model_training - INFO - AUC: 0.9591
2025-08-09 00:16:55 - model_training - INFO - AUPRC: 0.9570
2025-08-09 00:16:55 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:55 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 00:16:55 - model_training - INFO - 
分类报告:
2025-08-09 00:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:16:55 - model_training - INFO - 训练时间: 1.01 秒
2025-08-09 00:16:55 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-09 00:16:55 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\CatBoost_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:16:55 - model_training - INFO - 准确率: 0.8250
2025-08-09 00:16:55 - model_training - INFO - AUC: 0.9284
2025-08-09 00:16:55 - model_training - INFO - AUPRC: 0.9288
2025-08-09 00:16:55 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:55 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-09 00:16:55 - model_training - INFO - 
分类报告:
2025-08-09 00:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-09 00:16:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:16:55 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-09 00:16:55 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\Logistic_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型名称: SVM
2025-08-09 00:16:55 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:16:55 - model_training - INFO - AUC: 0.9182
2025-08-09 00:16:55 - model_training - INFO - AUPRC: 0.9034
2025-08-09 00:16:55 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:55 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-09 00:16:55 - model_training - INFO - 
分类报告:
2025-08-09 00:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-09 00:16:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:16:55 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-09 00:16:55 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\SVM_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型名称: KNN
2025-08-09 00:16:55 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:16:55 - model_training - INFO - AUC: 0.9322
2025-08-09 00:16:55 - model_training - INFO - AUPRC: 0.9189
2025-08-09 00:16:55 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:55 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 00:16:55 - model_training - INFO - 
分类报告:
2025-08-09 00:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:16:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:16:55 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-09 00:16:55 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\KNN_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:16:55 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:16:55 - model_training - INFO - AUC: 0.8977
2025-08-09 00:16:55 - model_training - INFO - AUPRC: 0.9096
2025-08-09 00:16:55 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:55 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-09 00:16:55 - model_training - INFO - 
分类报告:
2025-08-09 00:16:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:16:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:16:55 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-09 00:16:55 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\NaiveBayes_single_001655.joblib
2025-08-09 00:16:55 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:16:56 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:16:56 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:16:56 - model_training - INFO - AUC: 0.9591
2025-08-09 00:16:56 - model_training - INFO - AUPRC: 0.9450
2025-08-09 00:16:56 - model_training - INFO - 混淆矩阵:
2025-08-09 00:16:56 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 00:16:56 - model_training - INFO - 
分类报告:
2025-08-09 00:16:56 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:16:56 - model_training - INFO - 训练时间: 0.21 秒
2025-08-09 00:16:56 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-09 00:16:56 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_001654\models\NeuralNet_single_001656.joblib
2025-08-09 00:16:56 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 00:28:31 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:28:31 - model_training - INFO - 准确率: 0.7000
2025-08-09 00:28:31 - model_training - INFO - AUC: 0.7194
2025-08-09 00:28:31 - model_training - INFO - AUPRC: 0.5662
2025-08-09 00:28:31 - model_training - INFO - 混淆矩阵:
2025-08-09 00:28:31 - model_training - INFO - 
[[22  5]
 [ 7  6]]
2025-08-09 00:28:31 - model_training - INFO - 
分类报告:
2025-08-09 00:28:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.81      0.79        27
           1       0.55      0.46      0.50        13

    accuracy                           0.70        40
   macro avg       0.65      0.64      0.64        40
weighted avg       0.69      0.70      0.69        40

2025-08-09 00:28:31 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:28:31 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7000
2025-08-09 00:28:31 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_002831\models\DecisionTree_single_002831.joblib
2025-08-09 00:28:31 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:28:31 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:28:31 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:28:31 - model_training - INFO - AUC: 0.7764
2025-08-09 00:28:31 - model_training - INFO - AUPRC: 0.6007
2025-08-09 00:28:31 - model_training - INFO - 混淆矩阵:
2025-08-09 00:28:31 - model_training - INFO - 
[[23  4]
 [ 6  7]]
2025-08-09 00:28:31 - model_training - INFO - 
分类报告:
2025-08-09 00:28:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.85      0.82        27
           1       0.64      0.54      0.58        13

    accuracy                           0.75        40
   macro avg       0.71      0.70      0.70        40
weighted avg       0.74      0.75      0.74        40

2025-08-09 00:28:31 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 00:28:31 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7500
2025-08-09 00:28:31 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_002831\models\RandomForest_single_002831.joblib
2025-08-09 00:28:31 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:28:31 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.962 以处理不平衡
2025-08-09 00:28:31 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:28:31 - model_training - INFO - 准确率: 0.7250
2025-08-09 00:28:31 - model_training - INFO - AUC: 0.7350
2025-08-09 00:28:31 - model_training - INFO - AUPRC: 0.6248
2025-08-09 00:28:31 - model_training - INFO - 混淆矩阵:
2025-08-09 00:28:31 - model_training - INFO - 
[[23  4]
 [ 7  6]]
2025-08-09 00:28:31 - model_training - INFO - 
分类报告:
2025-08-09 00:28:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.77      0.85      0.81        27
           1       0.60      0.46      0.52        13

    accuracy                           0.72        40
   macro avg       0.68      0.66      0.66        40
weighted avg       0.71      0.72      0.71        40

2025-08-09 00:28:31 - model_training - INFO - 训练时间: 0.05 秒
2025-08-09 00:28:31 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.7250
2025-08-09 00:28:31 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_002831\models\XGBoost_single_002831.joblib
2025-08-09 00:28:31 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:28:31 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.962 以处理不平衡
2025-08-09 00:28:31 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:28:31 - model_training - INFO - 准确率: 0.7250
2025-08-09 00:28:31 - model_training - INFO - AUC: 0.7436
2025-08-09 00:28:31 - model_training - INFO - AUPRC: 0.6715
2025-08-09 00:28:31 - model_training - INFO - 混淆矩阵:
2025-08-09 00:28:31 - model_training - INFO - 
[[23  4]
 [ 7  6]]
2025-08-09 00:28:31 - model_training - INFO - 
分类报告:
2025-08-09 00:28:31 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.77      0.85      0.81        27
           1       0.60      0.46      0.52        13

    accuracy                           0.72        40
   macro avg       0.68      0.66      0.66        40
weighted avg       0.71      0.72      0.71        40

2025-08-09 00:28:31 - model_training - INFO - 训练时间: 0.06 秒
2025-08-09 00:28:31 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.7250
2025-08-09 00:28:31 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_002831\models\LightGBM_single_002831.joblib
2025-08-09 00:28:31 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:28:31 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:28:33 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:28:33 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:28:33 - model_training - INFO - AUC: 0.7749
2025-08-09 00:28:33 - model_training - INFO - AUPRC: 0.6143
2025-08-09 00:28:33 - model_training - INFO - 混淆矩阵:
2025-08-09 00:28:33 - model_training - INFO - 
[[23  4]
 [ 6  7]]
2025-08-09 00:28:33 - model_training - INFO - 
分类报告:
2025-08-09 00:28:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.85      0.82        27
           1       0.64      0.54      0.58        13

    accuracy                           0.75        40
   macro avg       0.71      0.70      0.70        40
weighted avg       0.74      0.75      0.74        40

2025-08-09 00:28:33 - model_training - INFO - 训练时间: 1.05 秒
2025-08-09 00:28:33 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.7500
2025-08-09 00:28:33 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_002831\models\CatBoost_single_002833.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:28:33 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:28:33 - model_training - INFO - AUC: 0.7635
2025-08-09 00:28:33 - model_training - INFO - AUPRC: 0.6501
2025-08-09 00:28:33 - model_training - INFO - 混淆矩阵:
2025-08-09 00:28:33 - model_training - INFO - 
[[23  4]
 [ 6  7]]
2025-08-09 00:28:33 - model_training - INFO - 
分类报告:
2025-08-09 00:28:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.85      0.82        27
           1       0.64      0.54      0.58        13

    accuracy                           0.75        40
   macro avg       0.71      0.70      0.70        40
weighted avg       0.74      0.75      0.74        40

2025-08-09 00:28:33 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:28:33 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7500
2025-08-09 00:28:33 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_002831\models\Logistic_single_002833.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型名称: SVM
2025-08-09 00:28:33 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:28:33 - model_training - INFO - AUC: 0.7436
2025-08-09 00:28:33 - model_training - INFO - AUPRC: 0.6505
2025-08-09 00:28:33 - model_training - INFO - 混淆矩阵:
2025-08-09 00:28:33 - model_training - INFO - 
[[23  4]
 [ 5  8]]
2025-08-09 00:28:33 - model_training - INFO - 
分类报告:
2025-08-09 00:28:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.85      0.84        27
           1       0.67      0.62      0.64        13

    accuracy                           0.78        40
   macro avg       0.74      0.73      0.74        40
weighted avg       0.77      0.78      0.77        40

2025-08-09 00:28:33 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:28:33 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-09 00:28:33 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_002831\models\SVM_single_002833.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型名称: KNN
2025-08-09 00:28:33 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:28:33 - model_training - INFO - AUC: 0.7892
2025-08-09 00:28:33 - model_training - INFO - AUPRC: 0.6166
2025-08-09 00:28:33 - model_training - INFO - 混淆矩阵:
2025-08-09 00:28:33 - model_training - INFO - 
[[22  5]
 [ 4  9]]
2025-08-09 00:28:33 - model_training - INFO - 
分类报告:
2025-08-09 00:28:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.81      0.83        27
           1       0.64      0.69      0.67        13

    accuracy                           0.78        40
   macro avg       0.74      0.75      0.75        40
weighted avg       0.78      0.78      0.78        40

2025-08-09 00:28:33 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:28:33 - model_training - INFO - 模型 KNN 性能: 准确率=0.7750
2025-08-09 00:28:33 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_002831\models\KNN_single_002833.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:28:33 - model_training - INFO - 准确率: 0.7000
2025-08-09 00:28:33 - model_training - INFO - AUC: 0.7493
2025-08-09 00:28:33 - model_training - INFO - AUPRC: 0.6235
2025-08-09 00:28:33 - model_training - INFO - 混淆矩阵:
2025-08-09 00:28:33 - model_training - INFO - 
[[24  3]
 [ 9  4]]
2025-08-09 00:28:33 - model_training - INFO - 
分类报告:
2025-08-09 00:28:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.73      0.89      0.80        27
           1       0.57      0.31      0.40        13

    accuracy                           0.70        40
   macro avg       0.65      0.60      0.60        40
weighted avg       0.68      0.70      0.67        40

2025-08-09 00:28:33 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:28:33 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.7000
2025-08-09 00:28:33 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_002831\models\NaiveBayes_single_002833.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:28:33 - model_training - INFO - 准确率: 0.6750
2025-08-09 00:28:33 - model_training - INFO - AUC: 0.7863
2025-08-09 00:28:33 - model_training - INFO - AUPRC: 0.6672
2025-08-09 00:28:33 - model_training - INFO - 混淆矩阵:
2025-08-09 00:28:33 - model_training - INFO - 
[[23  4]
 [ 9  4]]
2025-08-09 00:28:33 - model_training - INFO - 
分类报告:
2025-08-09 00:28:33 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.72      0.85      0.78        27
           1       0.50      0.31      0.38        13

    accuracy                           0.68        40
   macro avg       0.61      0.58      0.58        40
weighted avg       0.65      0.68      0.65        40

2025-08-09 00:28:33 - model_training - INFO - 训练时间: 0.49 秒
2025-08-09 00:28:33 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.6750
2025-08-09 00:28:33 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_002831\models\NeuralNet_single_002833.joblib
2025-08-09 00:28:33 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:36:58 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:36:58 - model_training - INFO - AUC: 0.8593
2025-08-09 00:36:58 - model_training - INFO - AUPRC: 0.8251
2025-08-09 00:36:58 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:58 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-08-09 00:36:58 - model_training - INFO - 
分类报告:
2025-08-09 00:36:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-08-09 00:36:58 - model_training - INFO - 训练时间: 0.00 秒
2025-08-09 00:36:58 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7750
2025-08-09 00:36:58 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\DecisionTree_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:36:58 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:36:58 - model_training - INFO - AUC: 0.8696
2025-08-09 00:36:58 - model_training - INFO - AUPRC: 0.8887
2025-08-09 00:36:58 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:58 - model_training - INFO - 
[[17  6]
 [ 4 13]]
2025-08-09 00:36:58 - model_training - INFO - 
分类报告:
2025-08-09 00:36:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.74      0.77        23
           1       0.68      0.76      0.72        17

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.76      0.75      0.75        40

2025-08-09 00:36:58 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 00:36:58 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7500
2025-08-09 00:36:58 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\RandomForest_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:36:58 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:36:58 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:36:58 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:36:58 - model_training - INFO - AUC: 0.8849
2025-08-09 00:36:58 - model_training - INFO - AUPRC: 0.8789
2025-08-09 00:36:58 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:58 - model_training - INFO - 
[[18  5]
 [ 4 13]]
2025-08-09 00:36:58 - model_training - INFO - 
分类报告:
2025-08-09 00:36:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.82      0.78      0.80        23
           1       0.72      0.76      0.74        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.78      0.78      0.78        40

2025-08-09 00:36:58 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:36:58 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.7750
2025-08-09 00:36:58 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\XGBoost_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:36:58 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:36:58 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:36:58 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:36:58 - model_training - INFO - AUC: 0.8645
2025-08-09 00:36:58 - model_training - INFO - AUPRC: 0.8724
2025-08-09 00:36:58 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:58 - model_training - INFO - 
[[17  6]
 [ 4 13]]
2025-08-09 00:36:58 - model_training - INFO - 
分类报告:
2025-08-09 00:36:58 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.74      0.77        23
           1       0.68      0.76      0.72        17

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.76      0.75      0.75        40

2025-08-09 00:36:58 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:36:58 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.7500
2025-08-09 00:36:58 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\LightGBM_single_003658.joblib
2025-08-09 00:36:58 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:36:58 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:36:59 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8747
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.8870
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[17  6]
 [ 4 13]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.81      0.74      0.77        23
           1       0.68      0.76      0.72        17

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.76      0.75      0.75        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 1.07 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.7500
2025-08-09 00:36:59 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\CatBoost_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8389
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.8490
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8000
2025-08-09 00:36:59 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\Logistic_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型名称: SVM
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8440
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.8150
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 SVM 性能: 准确率=0.8000
2025-08-09 00:36:59 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\SVM_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型名称: KNN
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8005
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.7075
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 KNN 性能: 准确率=0.7750
2025-08-09 00:36:59 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\KNN_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.8250
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8440
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.8481
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 0.00 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8250
2025-08-09 00:36:59 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\NaiveBayes_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:36:59 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:36:59 - model_training - INFO - AUC: 0.8542
2025-08-09 00:36:59 - model_training - INFO - AUPRC: 0.8330
2025-08-09 00:36:59 - model_training - INFO - 混淆矩阵:
2025-08-09 00:36:59 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-09 00:36:59 - model_training - INFO - 
分类报告:
2025-08-09 00:36:59 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-09 00:36:59 - model_training - INFO - 训练时间: 0.18 秒
2025-08-09 00:36:59 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.7750
2025-08-09 00:36:59 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_003658\models\NeuralNet_single_003659.joblib
2025-08-09 00:36:59 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 00:44:34 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:44:34 - model_training - INFO - 准确率: 0.7250
2025-08-09 00:44:34 - model_training - INFO - AUC: 0.8210
2025-08-09 00:44:34 - model_training - INFO - AUPRC: 0.6807
2025-08-09 00:44:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:34 - model_training - INFO - 
[[15  8]
 [ 3 14]]
2025-08-09 00:44:34 - model_training - INFO - 
分类报告:
2025-08-09 00:44:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.65      0.73        23
           1       0.64      0.82      0.72        17

    accuracy                           0.72        40
   macro avg       0.73      0.74      0.72        40
weighted avg       0.75      0.72      0.73        40

2025-08-09 00:44:34 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:44:34 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.7250
2025-08-09 00:44:34 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\DecisionTree_single_004434.joblib
2025-08-09 00:44:34 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:44:34 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:44:34 - model_training - INFO - 准确率: 0.8500
2025-08-09 00:44:34 - model_training - INFO - AUC: 0.9501
2025-08-09 00:44:34 - model_training - INFO - AUPRC: 0.9410
2025-08-09 00:44:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:34 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-09 00:44:34 - model_training - INFO - 
分类报告:
2025-08-09 00:44:34 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-09 00:44:34 - model_training - INFO - 训练时间: 0.07 秒
2025-08-09 00:44:34 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8500
2025-08-09 00:44:34 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\RandomForest_single_004434.joblib
2025-08-09 00:44:34 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:44:34 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:44:34 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:44:34 - model_training - INFO - 准确率: 0.8250
2025-08-09 00:44:34 - model_training - INFO - AUC: 0.9437
2025-08-09 00:44:34 - model_training - INFO - AUPRC: 0.9387
2025-08-09 00:44:34 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:34 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-09 00:44:34 - model_training - INFO - 
分类报告:
2025-08-09 00:44:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-09 00:44:35 - model_training - INFO - 训练时间: 0.05 秒
2025-08-09 00:44:35 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.8250
2025-08-09 00:44:35 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\XGBoost_single_004435.joblib
2025-08-09 00:44:35 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:44:35 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:44:35 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:44:35 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:44:35 - model_training - INFO - AUC: 0.9642
2025-08-09 00:44:35 - model_training - INFO - AUPRC: 0.9541
2025-08-09 00:44:35 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:35 - model_training - INFO - 
[[19  4]
 [ 1 16]]
2025-08-09 00:44:35 - model_training - INFO - 
分类报告:
2025-08-09 00:44:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.95      0.83      0.88        23
           1       0.80      0.94      0.86        17

    accuracy                           0.88        40
   macro avg       0.88      0.88      0.87        40
weighted avg       0.89      0.88      0.88        40

2025-08-09 00:44:35 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:44:35 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8750
2025-08-09 00:44:35 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\LightGBM_single_004435.joblib
2025-08-09 00:44:35 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:44:35 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:44:36 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9540
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.9486
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 1.12 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8000
2025-08-09 00:44:36 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\CatBoost_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.7750
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9028
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.8685
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[17  6]
 [ 3 14]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.74      0.79        23
           1       0.70      0.82      0.76        17

    accuracy                           0.78        40
   macro avg       0.77      0.78      0.77        40
weighted avg       0.79      0.78      0.78        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 Logistic 性能: 准确率=0.7750
2025-08-09 00:44:36 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\Logistic_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型名称: SVM
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.8250
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9258
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.9259
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[17  6]
 [ 1 16]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.94      0.74      0.83        23
           1       0.73      0.94      0.82        17

    accuracy                           0.82        40
   macro avg       0.84      0.84      0.82        40
weighted avg       0.85      0.82      0.83        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 SVM 性能: 准确率=0.8250
2025-08-09 00:44:36 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\SVM_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型名称: KNN
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9054
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.8760
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[18  5]
 [ 3 14]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.86      0.78      0.82        23
           1       0.74      0.82      0.78        17

    accuracy                           0.80        40
   macro avg       0.80      0.80      0.80        40
weighted avg       0.81      0.80      0.80        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 KNN 性能: 准确率=0.8000
2025-08-09 00:44:36 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\KNN_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.8500
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9156
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.8954
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8500
2025-08-09 00:44:36 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\NaiveBayes_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:44:36 - model_training - INFO - 准确率: 0.8500
2025-08-09 00:44:36 - model_training - INFO - AUC: 0.9437
2025-08-09 00:44:36 - model_training - INFO - AUPRC: 0.9159
2025-08-09 00:44:36 - model_training - INFO - 混淆矩阵:
2025-08-09 00:44:36 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-09 00:44:36 - model_training - INFO - 
分类报告:
2025-08-09 00:44:36 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-09 00:44:36 - model_training - INFO - 训练时间: 0.15 秒
2025-08-09 00:44:36 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8500
2025-08-09 00:44:36 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_004434\models\NeuralNet_single_004436.joblib
2025-08-09 00:44:36 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 00:54:20 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 00:54:20 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:54:20 - model_training - INFO - AUC: 0.9309
2025-08-09 00:54:20 - model_training - INFO - AUPRC: 0.8875
2025-08-09 00:54:20 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:20 - model_training - INFO - 
[[20  3]
 [ 2 15]]
2025-08-09 00:54:20 - model_training - INFO - 
分类报告:
2025-08-09 00:54:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.87      0.89        23
           1       0.83      0.88      0.86        17

    accuracy                           0.88        40
   macro avg       0.87      0.88      0.87        40
weighted avg       0.88      0.88      0.88        40

2025-08-09 00:54:20 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:54:20 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8750
2025-08-09 00:54:20 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\DecisionTree_single_005420.joblib
2025-08-09 00:54:20 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 00:54:20 - model_training - INFO - 模型名称: Random Forest
2025-08-09 00:54:20 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:54:20 - model_training - INFO - AUC: 0.9399
2025-08-09 00:54:20 - model_training - INFO - AUPRC: 0.9432
2025-08-09 00:54:20 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:20 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 00:54:20 - model_training - INFO - 
分类报告:
2025-08-09 00:54:20 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 00:54:20 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 00:54:20 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 00:54:21 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\RandomForest_single_005420.joblib
2025-08-09 00:54:21 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 00:54:21 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:54:21 - model_training - INFO - 模型名称: XGBoost
2025-08-09 00:54:21 - model_training - INFO - 准确率: 0.9250
2025-08-09 00:54:21 - model_training - INFO - AUC: 0.9540
2025-08-09 00:54:21 - model_training - INFO - AUPRC: 0.8766
2025-08-09 00:54:21 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:21 - model_training - INFO - 
[[22  1]
 [ 2 15]]
2025-08-09 00:54:21 - model_training - INFO - 
分类报告:
2025-08-09 00:54:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.92      0.96      0.94        23
           1       0.94      0.88      0.91        17

    accuracy                           0.93        40
   macro avg       0.93      0.92      0.92        40
weighted avg       0.93      0.93      0.92        40

2025-08-09 00:54:21 - model_training - INFO - 训练时间: 0.05 秒
2025-08-09 00:54:21 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9250
2025-08-09 00:54:21 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\XGBoost_single_005421.joblib
2025-08-09 00:54:21 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 00:54:21 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 00:54:21 - model_training - INFO - 模型名称: LightGBM
2025-08-09 00:54:21 - model_training - INFO - 准确率: 0.9250
2025-08-09 00:54:21 - model_training - INFO - AUC: 0.9770
2025-08-09 00:54:21 - model_training - INFO - AUPRC: 0.9665
2025-08-09 00:54:21 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:21 - model_training - INFO - 
[[22  1]
 [ 2 15]]
2025-08-09 00:54:21 - model_training - INFO - 
分类报告:
2025-08-09 00:54:21 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.92      0.96      0.94        23
           1       0.94      0.88      0.91        17

    accuracy                           0.93        40
   macro avg       0.93      0.92      0.92        40
weighted avg       0.93      0.93      0.92        40

2025-08-09 00:54:21 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 00:54:21 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.9250
2025-08-09 00:54:21 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\LightGBM_single_005421.joblib
2025-08-09 00:54:21 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 00:54:21 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 00:54:22 - model_training - INFO - 模型名称: CatBoost
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.9250
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.9821
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.9795
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[22  1]
 [ 2 15]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.92      0.96      0.94        23
           1       0.94      0.88      0.91        17

    accuracy                           0.93        40
   macro avg       0.93      0.92      0.92        40
weighted avg       0.93      0.93      0.92        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 1.11 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.9250
2025-08-09 00:54:22 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\CatBoost_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.8500
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.9207
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.9287
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[19  4]
 [ 2 15]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.83      0.86        23
           1       0.79      0.88      0.83        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.86      0.85      0.85        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8500
2025-08-09 00:54:22 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\Logistic_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型名称: SVM
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.8000
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.9003
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.9003
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[17  6]
 [ 2 15]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.89      0.74      0.81        23
           1       0.71      0.88      0.79        17

    accuracy                           0.80        40
   macro avg       0.80      0.81      0.80        40
weighted avg       0.82      0.80      0.80        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 SVM 性能: 准确率=0.8000
2025-08-09 00:54:22 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\SVM_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型名称: KNN
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.8250
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.8747
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.7775
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[18  5]
 [ 2 15]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.78      0.84        23
           1       0.75      0.88      0.81        17

    accuracy                           0.82        40
   macro avg       0.82      0.83      0.82        40
weighted avg       0.84      0.82      0.83        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 KNN 性能: 准确率=0.8250
2025-08-09 00:54:22 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\KNN_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.7500
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.8555
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.7533
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[19  4]
 [ 6 11]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.76      0.83      0.79        23
           1       0.73      0.65      0.69        17

    accuracy                           0.75        40
   macro avg       0.75      0.74      0.74        40
weighted avg       0.75      0.75      0.75        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.7500
2025-08-09 00:54:22 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\NaiveBayes_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型名称: Neural Network
2025-08-09 00:54:22 - model_training - INFO - 准确率: 0.8750
2025-08-09 00:54:22 - model_training - INFO - AUC: 0.9335
2025-08-09 00:54:22 - model_training - INFO - AUPRC: 0.8779
2025-08-09 00:54:22 - model_training - INFO - 混淆矩阵:
2025-08-09 00:54:22 - model_training - INFO - 
[[20  3]
 [ 2 15]]
2025-08-09 00:54:22 - model_training - INFO - 
分类报告:
2025-08-09 00:54:22 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.87      0.89        23
           1       0.83      0.88      0.86        17

    accuracy                           0.88        40
   macro avg       0.87      0.88      0.87        40
weighted avg       0.88      0.88      0.88        40

2025-08-09 00:54:22 - model_training - INFO - 训练时间: 0.21 秒
2025-08-09 00:54:22 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-09 00:54:22 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_005420\models\NeuralNet_single_005422.joblib
2025-08-09 00:54:22 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 01:27:50 - model_training - INFO - 模型名称: Random Forest
2025-08-09 01:27:50 - model_training - INFO - 准确率: 0.7000
2025-08-09 01:27:50 - model_training - INFO - AUC: 0.8313
2025-08-09 01:27:50 - model_training - INFO - AUPRC: 0.8569
2025-08-09 01:27:50 - model_training - INFO - 混淆矩阵:
2025-08-09 01:27:50 - model_training - INFO - 
[[13  7]
 [ 5 15]]
2025-08-09 01:27:50 - model_training - INFO - 
分类报告:
2025-08-09 01:27:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.72      0.65      0.68        20
           1       0.68      0.75      0.71        20

    accuracy                           0.70        40
   macro avg       0.70      0.70      0.70        40
weighted avg       0.70      0.70      0.70        40

2025-08-09 01:27:50 - model_training - INFO - 训练时间: 0.22 秒
2025-08-09 01:27:50 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7000
2025-08-09 01:27:50 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 01:27:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-09 01:28:35 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.000 以处理不平衡
2025-08-09 01:28:35 - model_training - INFO - 模型名称: XGBoost
2025-08-09 01:28:35 - model_training - INFO - 准确率: 0.7500
2025-08-09 01:28:35 - model_training - INFO - AUC: 0.8325
2025-08-09 01:28:35 - model_training - INFO - AUPRC: 0.8542
2025-08-09 01:28:35 - model_training - INFO - 混淆矩阵:
2025-08-09 01:28:35 - model_training - INFO - 
[[15  5]
 [ 5 15]]
2025-08-09 01:28:35 - model_training - INFO - 
分类报告:
2025-08-09 01:28:35 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.75      0.75      0.75        20
           1       0.75      0.75      0.75        20

    accuracy                           0.75        40
   macro avg       0.75      0.75      0.75        40
weighted avg       0.75      0.75      0.75        40

2025-08-09 01:28:35 - model_training - INFO - 训练时间: 0.09 秒
2025-08-09 01:28:35 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.7500
2025-08-09 01:28:35 - model_training - INFO - 模型 XGBoost 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 01:28:35 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_feature_names.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型名称: Random Forest
2025-08-09 01:52:10 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:52:10 - model_training - INFO - AUC: 0.9412
2025-08-09 01:52:10 - model_training - INFO - AUPRC: 0.9359
2025-08-09 01:52:10 - model_training - INFO - 混淆矩阵:
2025-08-09 01:52:10 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:52:10 - model_training - INFO - 
分类报告:
2025-08-09 01:52:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:52:10 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 01:52:10 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 01:52:10 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015210\models\RandomForest_single_015210.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型名称: SVM
2025-08-09 01:52:10 - model_training - INFO - 准确率: 0.7750
2025-08-09 01:52:10 - model_training - INFO - AUC: 0.9182
2025-08-09 01:52:10 - model_training - INFO - AUPRC: 0.9034
2025-08-09 01:52:10 - model_training - INFO - 混淆矩阵:
2025-08-09 01:52:10 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-09 01:52:10 - model_training - INFO - 
分类报告:
2025-08-09 01:52:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-09 01:52:10 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:52:10 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-09 01:52:10 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015210\models\SVM_single_015210.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型名称: KNN
2025-08-09 01:52:10 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:52:10 - model_training - INFO - AUC: 0.9322
2025-08-09 01:52:10 - model_training - INFO - AUPRC: 0.9189
2025-08-09 01:52:10 - model_training - INFO - 混淆矩阵:
2025-08-09 01:52:10 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:52:10 - model_training - INFO - 
分类报告:
2025-08-09 01:52:10 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:52:10 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:52:10 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-09 01:52:10 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015210\models\KNN_single_015210.joblib
2025-08-09 01:52:10 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型名称: Decision Tree
2025-08-09 01:55:12 - model_training - INFO - 准确率: 0.8000
2025-08-09 01:55:12 - model_training - INFO - AUC: 0.8951
2025-08-09 01:55:12 - model_training - INFO - AUPRC: 0.7948
2025-08-09 01:55:12 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:12 - model_training - INFO - 
[[20  3]
 [ 5 12]]
2025-08-09 01:55:12 - model_training - INFO - 
分类报告:
2025-08-09 01:55:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.87      0.83        23
           1       0.80      0.71      0.75        17

    accuracy                           0.80        40
   macro avg       0.80      0.79      0.79        40
weighted avg       0.80      0.80      0.80        40

2025-08-09 01:55:12 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:55:12 - model_training - INFO - 模型 DecisionTree 性能: 准确率=0.8000
2025-08-09 01:55:12 - model_training - INFO - 模型 DecisionTree 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\DecisionTree_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 DecisionTree 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\DecisionTree_results.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型名称: Random Forest
2025-08-09 01:55:12 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:55:12 - model_training - INFO - AUC: 0.9412
2025-08-09 01:55:12 - model_training - INFO - AUPRC: 0.9359
2025-08-09 01:55:12 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:12 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:55:12 - model_training - INFO - 
分类报告:
2025-08-09 01:55:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:55:12 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 01:55:12 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 01:55:12 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\RandomForest_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 01:55:12 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 01:55:12 - model_training - INFO - 模型名称: XGBoost
2025-08-09 01:55:12 - model_training - INFO - 准确率: 0.9000
2025-08-09 01:55:12 - model_training - INFO - AUC: 0.9719
2025-08-09 01:55:12 - model_training - INFO - AUPRC: 0.9627
2025-08-09 01:55:12 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:12 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-09 01:55:12 - model_training - INFO - 
分类报告:
2025-08-09 01:55:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-09 01:55:12 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 01:55:12 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-09 01:55:12 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\XGBoost_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 01:55:12 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 01:55:12 - model_training - INFO - 模型名称: LightGBM
2025-08-09 01:55:12 - model_training - INFO - 准确率: 0.8500
2025-08-09 01:55:12 - model_training - INFO - AUC: 0.9488
2025-08-09 01:55:12 - model_training - INFO - AUPRC: 0.9492
2025-08-09 01:55:12 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:12 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-09 01:55:12 - model_training - INFO - 
分类报告:
2025-08-09 01:55:12 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-09 01:55:12 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 01:55:12 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-09 01:55:12 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\LightGBM_single_015512.joblib
2025-08-09 01:55:12 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 01:55:12 - model_training - INFO - [CatBoost] 启用 auto_class_weights='Balanced' 以处理不平衡
2025-08-09 01:55:13 - model_training - INFO - 模型名称: CatBoost
2025-08-09 01:55:13 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:55:13 - model_training - INFO - AUC: 0.9591
2025-08-09 01:55:13 - model_training - INFO - AUPRC: 0.9570
2025-08-09 01:55:13 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:13 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:55:13 - model_training - INFO - 
分类报告:
2025-08-09 01:55:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:55:13 - model_training - INFO - 训练时间: 1.03 秒
2025-08-09 01:55:13 - model_training - INFO - 模型 CatBoost 性能: 准确率=0.8750
2025-08-09 01:55:13 - model_training - INFO - 模型 CatBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\CatBoost_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 CatBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\CatBoost_results.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 01:55:13 - model_training - INFO - 准确率: 0.8250
2025-08-09 01:55:13 - model_training - INFO - AUC: 0.9284
2025-08-09 01:55:13 - model_training - INFO - AUPRC: 0.9288
2025-08-09 01:55:13 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:13 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-09 01:55:13 - model_training - INFO - 
分类报告:
2025-08-09 01:55:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-09 01:55:13 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:55:13 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-09 01:55:13 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\Logistic_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型名称: SVM
2025-08-09 01:55:13 - model_training - INFO - 准确率: 0.7750
2025-08-09 01:55:13 - model_training - INFO - AUC: 0.9182
2025-08-09 01:55:13 - model_training - INFO - AUPRC: 0.9034
2025-08-09 01:55:13 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:13 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-09 01:55:13 - model_training - INFO - 
分类报告:
2025-08-09 01:55:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-09 01:55:13 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:55:13 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-09 01:55:13 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\SVM_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型名称: KNN
2025-08-09 01:55:13 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:55:13 - model_training - INFO - AUC: 0.9322
2025-08-09 01:55:13 - model_training - INFO - AUPRC: 0.9189
2025-08-09 01:55:13 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:13 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:55:13 - model_training - INFO - 
分类报告:
2025-08-09 01:55:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:55:13 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:55:13 - model_training - INFO - 模型 KNN 性能: 准确率=0.8750
2025-08-09 01:55:13 - model_training - INFO - 模型 KNN 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\KNN_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 KNN 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\KNN_results.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型名称: Naive Bayes
2025-08-09 01:55:13 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:55:13 - model_training - INFO - AUC: 0.8977
2025-08-09 01:55:13 - model_training - INFO - AUPRC: 0.9096
2025-08-09 01:55:13 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:13 - model_training - INFO - 
[[22  1]
 [ 4 13]]
2025-08-09 01:55:13 - model_training - INFO - 
分类报告:
2025-08-09 01:55:13 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.85      0.96      0.90        23
           1       0.93      0.76      0.84        17

    accuracy                           0.88        40
   macro avg       0.89      0.86      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:55:13 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 01:55:13 - model_training - INFO - 模型 NaiveBayes 性能: 准确率=0.8750
2025-08-09 01:55:13 - model_training - INFO - 模型 NaiveBayes 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\NaiveBayes_single_015513.joblib
2025-08-09 01:55:13 - model_training - INFO - 模型 NaiveBayes 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NaiveBayes_results.joblib
2025-08-09 01:55:14 - model_training - INFO - 模型名称: Neural Network
2025-08-09 01:55:14 - model_training - INFO - 准确率: 0.8750
2025-08-09 01:55:14 - model_training - INFO - AUC: 0.9591
2025-08-09 01:55:14 - model_training - INFO - AUPRC: 0.9450
2025-08-09 01:55:14 - model_training - INFO - 混淆矩阵:
2025-08-09 01:55:14 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 01:55:14 - model_training - INFO - 
分类报告:
2025-08-09 01:55:14 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 01:55:14 - model_training - INFO - 训练时间: 0.22 秒
2025-08-09 01:55:14 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-09 01:55:14 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_015512\models\NeuralNet_single_015514.joblib
2025-08-09 01:55:14 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型名称: Random Forest
2025-08-09 03:07:25 - model_training - INFO - 准确率: 0.8750
2025-08-09 03:07:25 - model_training - INFO - AUC: 0.9412
2025-08-09 03:07:25 - model_training - INFO - AUPRC: 0.9359
2025-08-09 03:07:25 - model_training - INFO - 混淆矩阵:
2025-08-09 03:07:25 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 03:07:25 - model_training - INFO - 
分类报告:
2025-08-09 03:07:25 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 03:07:25 - model_training - INFO - 训练时间: 0.09 秒
2025-08-09 03:07:25 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 03:07:25 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_030725\models\RandomForest_single_030725.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型名称: SVM
2025-08-09 03:07:25 - model_training - INFO - 准确率: 0.7750
2025-08-09 03:07:25 - model_training - INFO - AUC: 0.9182
2025-08-09 03:07:25 - model_training - INFO - AUPRC: 0.9034
2025-08-09 03:07:25 - model_training - INFO - 混淆矩阵:
2025-08-09 03:07:25 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-09 03:07:25 - model_training - INFO - 
分类报告:
2025-08-09 03:07:25 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-09 03:07:25 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 03:07:25 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-09 03:07:25 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_030725\models\SVM_single_030725.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型名称: Neural Network
2025-08-09 03:07:25 - model_training - INFO - 准确率: 0.8750
2025-08-09 03:07:25 - model_training - INFO - AUC: 0.9591
2025-08-09 03:07:25 - model_training - INFO - AUPRC: 0.9450
2025-08-09 03:07:25 - model_training - INFO - 混淆矩阵:
2025-08-09 03:07:25 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 03:07:25 - model_training - INFO - 
分类报告:
2025-08-09 03:07:25 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 03:07:25 - model_training - INFO - 训练时间: 0.24 秒
2025-08-09 03:07:25 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-09 03:07:25 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_030725\models\NeuralNet_single_030725.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 22:22:57 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:22:57 - model_training - INFO - 准确率: 0.9250
2025-08-09 22:22:57 - model_training - INFO - AUC: 0.9973
2025-08-09 22:22:57 - model_training - INFO - AUPRC: 0.9985
2025-08-09 22:22:57 - model_training - INFO - 混淆矩阵:
2025-08-09 22:22:57 - model_training - INFO - 
[[15  0]
 [ 3 22]]
2025-08-09 22:22:57 - model_training - INFO - 
分类报告:
2025-08-09 22:22:57 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      1.00      0.91        15
           1       1.00      0.88      0.94        25

    accuracy                           0.93        40
   macro avg       0.92      0.94      0.92        40
weighted avg       0.94      0.93      0.93        40

2025-08-09 22:22:57 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:22:57 - model_training - INFO - 模型 Logistic 性能: 准确率=0.9250
2025-08-09 22:22:57 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:22:57 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:26:25 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:26:25 - model_training - INFO - 准确率: 0.9500
2025-08-09 22:26:25 - model_training - INFO - AUC: 0.9800
2025-08-09 22:26:25 - model_training - INFO - AUPRC: 0.9809
2025-08-09 22:26:25 - model_training - INFO - 混淆矩阵:
2025-08-09 22:26:25 - model_training - INFO - 
[[ 9  1]
 [ 0 10]]
2025-08-09 22:26:25 - model_training - INFO - 
分类报告:
2025-08-09 22:26:25 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.90      0.95        10
           1       0.91      1.00      0.95        10

    accuracy                           0.95        20
   macro avg       0.95      0.95      0.95        20
weighted avg       0.95      0.95      0.95        20

2025-08-09 22:26:25 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 22:26:25 - model_training - INFO - 模型 Logistic 性能: 准确率=0.9500
2025-08-09 22:26:25 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:26:25 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:44:50 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:44:50 - model_training - INFO - 准确率: 0.8750
2025-08-09 22:44:50 - model_training - INFO - AUC: 0.9790
2025-08-09 22:44:50 - model_training - INFO - AUPRC: 0.9838
2025-08-09 22:44:50 - model_training - INFO - 混淆矩阵:
2025-08-09 22:44:50 - model_training - INFO - 
[[ 9  2]
 [ 1 12]]
2025-08-09 22:44:50 - model_training - INFO - 
分类报告:
2025-08-09 22:44:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.82      0.86        11
           1       0.86      0.92      0.89        13

    accuracy                           0.88        24
   macro avg       0.88      0.87      0.87        24
weighted avg       0.88      0.88      0.87        24

2025-08-09 22:44:50 - model_training - INFO - 训练时间: 0.02 秒
2025-08-09 22:44:50 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8750
2025-08-09 22:44:50 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:44:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:44:50 - model_training - INFO - 模型名称: SVM
2025-08-09 22:44:50 - model_training - INFO - 准确率: 0.7917
2025-08-09 22:44:50 - model_training - INFO - AUC: 0.9301
2025-08-09 22:44:50 - model_training - INFO - AUPRC: 0.9477
2025-08-09 22:44:50 - model_training - INFO - 混淆矩阵:
2025-08-09 22:44:50 - model_training - INFO - 
[[ 8  3]
 [ 2 11]]
2025-08-09 22:44:50 - model_training - INFO - 
分类报告:
2025-08-09 22:44:50 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.80      0.73      0.76        11
           1       0.79      0.85      0.81        13

    accuracy                           0.79        24
   macro avg       0.79      0.79      0.79        24
weighted avg       0.79      0.79      0.79        24

2025-08-09 22:44:50 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:44:50 - model_training - INFO - 模型 SVM 性能: 准确率=0.7917
2025-08-09 22:44:50 - model_training - INFO - 模型 SVM 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 22:44:50 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_feature_names.joblib
2025-08-09 22:44:51 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:44:51 - model_training - INFO - 准确率: 0.8750
2025-08-09 22:44:51 - model_training - INFO - AUC: 0.9790
2025-08-09 22:44:51 - model_training - INFO - AUPRC: 0.9838
2025-08-09 22:44:51 - model_training - INFO - 混淆矩阵:
2025-08-09 22:44:51 - model_training - INFO - 
[[ 9  2]
 [ 1 12]]
2025-08-09 22:44:51 - model_training - INFO - 
分类报告:
2025-08-09 22:44:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.90      0.82      0.86        11
           1       0.86      0.92      0.89        13

    accuracy                           0.88        24
   macro avg       0.88      0.87      0.87        24
weighted avg       0.88      0.88      0.87        24

2025-08-09 22:44:51 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:44:51 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8750
2025-08-09 22:44:51 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:44:51 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:44:51 - model_training - INFO - 模型名称: Random Forest
2025-08-09 22:44:51 - model_training - INFO - 准确率: 0.7500
2025-08-09 22:44:51 - model_training - INFO - AUC: 0.9126
2025-08-09 22:44:51 - model_training - INFO - AUPRC: 0.9352
2025-08-09 22:44:51 - model_training - INFO - 混淆矩阵:
2025-08-09 22:44:51 - model_training - INFO - 
[[ 8  3]
 [ 3 10]]
2025-08-09 22:44:51 - model_training - INFO - 
分类报告:
2025-08-09 22:44:51 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.73      0.73      0.73        11
           1       0.77      0.77      0.77        13

    accuracy                           0.75        24
   macro avg       0.75      0.75      0.75        24
weighted avg       0.75      0.75      0.75        24

2025-08-09 22:44:51 - model_training - INFO - 训练时间: 0.09 秒
2025-08-09 22:44:51 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.7500
2025-08-09 22:44:51 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 22:44:51 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-09 22:45:54 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:45:54 - model_training - INFO - 准确率: 1.0000
2025-08-09 22:45:54 - model_training - INFO - AUC: 1.0000
2025-08-09 22:45:54 - model_training - INFO - AUPRC: 1.0000
2025-08-09 22:45:54 - model_training - INFO - 混淆矩阵:
2025-08-09 22:45:54 - model_training - INFO - 
[[ 7  0]
 [ 0 13]]
2025-08-09 22:45:54 - model_training - INFO - 
分类报告:
2025-08-09 22:45:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      1.00      1.00         7
           1       1.00      1.00      1.00        13

    accuracy                           1.00        20
   macro avg       1.00      1.00      1.00        20
weighted avg       1.00      1.00      1.00        20

2025-08-09 22:45:54 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:45:54 - model_training - INFO - 模型 Logistic 性能: 准确率=1.0000
2025-08-09 22:45:54 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:45:54 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:45:55 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:45:55 - model_training - INFO - 准确率: 1.0000
2025-08-09 22:45:55 - model_training - INFO - AUC: 1.0000
2025-08-09 22:45:55 - model_training - INFO - AUPRC: 1.0000
2025-08-09 22:45:55 - model_training - INFO - 混淆矩阵:
2025-08-09 22:45:55 - model_training - INFO - 
[[ 7  0]
 [ 0 13]]
2025-08-09 22:45:55 - model_training - INFO - 
分类报告:
2025-08-09 22:45:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      1.00      1.00         7
           1       1.00      1.00      1.00        13

    accuracy                           1.00        20
   macro avg       1.00      1.00      1.00        20
weighted avg       1.00      1.00      1.00        20

2025-08-09 22:45:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:45:55 - model_training - INFO - 模型 Logistic 性能: 准确率=1.0000
2025-08-09 22:45:55 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:45:55 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:45:55 - model_training - INFO - 模型名称: Random Forest
2025-08-09 22:45:55 - model_training - INFO - 准确率: 0.8000
2025-08-09 22:45:55 - model_training - INFO - AUC: 0.9011
2025-08-09 22:45:55 - model_training - INFO - AUPRC: 0.9387
2025-08-09 22:45:55 - model_training - INFO - 混淆矩阵:
2025-08-09 22:45:55 - model_training - INFO - 
[[ 3  4]
 [ 0 13]]
2025-08-09 22:45:55 - model_training - INFO - 
分类报告:
2025-08-09 22:45:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.43      0.60         7
           1       0.76      1.00      0.87        13

    accuracy                           0.80        20
   macro avg       0.88      0.71      0.73        20
weighted avg       0.85      0.80      0.77        20

2025-08-09 22:45:55 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 22:45:55 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8000
2025-08-09 22:45:55 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 22:45:55 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-09 22:53:24 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:53:24 - model_training - INFO - 准确率: 1.0000
2025-08-09 22:53:24 - model_training - INFO - AUC: 1.0000
2025-08-09 22:53:24 - model_training - INFO - AUPRC: 1.0000
2025-08-09 22:53:24 - model_training - INFO - 混淆矩阵:
2025-08-09 22:53:24 - model_training - INFO - 
[[8 0]
 [0 8]]
2025-08-09 22:53:24 - model_training - INFO - 
分类报告:
2025-08-09 22:53:24 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      1.00      1.00         8
           1       1.00      1.00      1.00         8

    accuracy                           1.00        16
   macro avg       1.00      1.00      1.00        16
weighted avg       1.00      1.00      1.00        16

2025-08-09 22:53:24 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:53:24 - model_training - INFO - 模型 Logistic 性能: 准确率=1.0000
2025-08-09 22:53:24 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:53:24 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:54:06 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:54:06 - model_training - INFO - 准确率: 1.0000
2025-08-09 22:54:06 - model_training - INFO - AUC: 1.0000
2025-08-09 22:54:06 - model_training - INFO - AUPRC: 1.0000
2025-08-09 22:54:06 - model_training - INFO - 混淆矩阵:
2025-08-09 22:54:06 - model_training - INFO - 
[[6 0]
 [0 6]]
2025-08-09 22:54:06 - model_training - INFO - 
分类报告:
2025-08-09 22:54:06 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      1.00      1.00         6
           1       1.00      1.00      1.00         6

    accuracy                           1.00        12
   macro avg       1.00      1.00      1.00        12
weighted avg       1.00      1.00      1.00        12

2025-08-09 22:54:06 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:54:06 - model_training - INFO - 模型 Logistic 性能: 准确率=1.0000
2025-08-09 22:54:06 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:54:06 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型名称: Random Forest
2025-08-09 22:57:37 - model_training - INFO - 准确率: 0.8750
2025-08-09 22:57:37 - model_training - INFO - AUC: 0.9412
2025-08-09 22:57:37 - model_training - INFO - AUPRC: 0.9359
2025-08-09 22:57:37 - model_training - INFO - 混淆矩阵:
2025-08-09 22:57:37 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 22:57:37 - model_training - INFO - 
分类报告:
2025-08-09 22:57:37 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 22:57:37 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 22:57:37 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 22:57:37 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\RandomForest_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 22:57:37 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 22:57:37 - model_training - INFO - 模型名称: XGBoost
2025-08-09 22:57:37 - model_training - INFO - 准确率: 0.9000
2025-08-09 22:57:37 - model_training - INFO - AUC: 0.9719
2025-08-09 22:57:37 - model_training - INFO - AUPRC: 0.9627
2025-08-09 22:57:37 - model_training - INFO - 混淆矩阵:
2025-08-09 22:57:37 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-09 22:57:37 - model_training - INFO - 
分类报告:
2025-08-09 22:57:37 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-09 22:57:37 - model_training - INFO - 训练时间: 0.05 秒
2025-08-09 22:57:37 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-09 22:57:37 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\XGBoost_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 22:57:37 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 22:57:37 - model_training - INFO - 模型名称: LightGBM
2025-08-09 22:57:37 - model_training - INFO - 准确率: 0.8500
2025-08-09 22:57:37 - model_training - INFO - AUC: 0.9488
2025-08-09 22:57:37 - model_training - INFO - AUPRC: 0.9492
2025-08-09 22:57:37 - model_training - INFO - 混淆矩阵:
2025-08-09 22:57:37 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-09 22:57:37 - model_training - INFO - 
分类报告:
2025-08-09 22:57:37 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-09 22:57:37 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 22:57:37 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-09 22:57:37 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\LightGBM_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:57:37 - model_training - INFO - 准确率: 0.8250
2025-08-09 22:57:37 - model_training - INFO - AUC: 0.9284
2025-08-09 22:57:37 - model_training - INFO - AUPRC: 0.9288
2025-08-09 22:57:37 - model_training - INFO - 混淆矩阵:
2025-08-09 22:57:37 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-09 22:57:37 - model_training - INFO - 
分类报告:
2025-08-09 22:57:37 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-09 22:57:37 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:57:37 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-09 22:57:37 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\Logistic_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
