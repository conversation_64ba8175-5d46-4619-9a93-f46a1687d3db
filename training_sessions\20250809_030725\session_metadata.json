{"session_id": "20250809_030725", "session_name": "训练_nodule2_20250809_030725", "description": "自动创建的训练会话，基于数据文件: nodule2", "created_time": "2025-08-09T03:07:25.301942", "last_modified": "2025-08-09T03:07:25.745026", "trained_models": [{"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_030725.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_030725\\models\\RandomForest_single_030725.joblib", "save_time": "2025-08-09T03:07:25.417528"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_030725.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_030725\\models\\SVM_single_030725.joblib", "save_time": "2025-08-09T03:07:25.478525"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_030725.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_030725\\models\\NeuralNet_single_030725.joblib", "save_time": "2025-08-09T03:07:25.734519"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}