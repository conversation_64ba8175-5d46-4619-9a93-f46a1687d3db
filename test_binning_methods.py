#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分箱方法验证脚本 - 测试完成后删除
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from code.data_exploration import DataExplorer

def generate_test_data(n_samples=200):
    """生成测试数据"""
    np.random.seed(42)
    
    # 生成不同类型的特征
    # 特征1：正态分布
    feature1 = np.random.normal(50, 15, n_samples)
    
    # 特征2：偏态分布
    feature2 = np.random.exponential(2, n_samples)
    
    # 特征3：有很多重复值的特征
    feature3 = np.random.choice([1, 2, 3, 4, 5], n_samples, p=[0.4, 0.3, 0.15, 0.1, 0.05])
    
    # 特征4：均匀分布
    feature4 = np.random.uniform(0, 100, n_samples)
    
    # 生成目标变量（与特征有一定关系）
    target_prob = (
        0.3 + 
        0.002 * feature1 + 
        0.1 * np.log(feature2 + 1) + 
        0.05 * feature3 + 
        0.001 * feature4
    )
    target_prob = np.clip(target_prob, 0.1, 0.9)
    target = np.random.binomial(1, target_prob, n_samples)
    
    # 添加一些缺失值
    missing_indices = np.random.choice(n_samples, size=int(0.05 * n_samples), replace=False)
    feature1[missing_indices[:len(missing_indices)//2]] = np.nan
    feature2[missing_indices[len(missing_indices)//2:]] = np.nan
    
    df = pd.DataFrame({
        'feature1_normal': feature1,
        'feature2_skewed': feature2,
        'feature3_discrete': feature3,
        'feature4_uniform': feature4,
        'target': target
    })
    
    return df

def test_binning_methods():
    """测试所有分箱方法"""
    print("=== 分箱方法验证测试 ===")
    
    # 生成测试数据
    df = generate_test_data(200)
    print(f"生成测试数据: {df.shape}")
    print(f"目标变量分布: {df['target'].value_counts().to_dict()}")
    print(f"缺失值情况:")
    print(df.isnull().sum())
    
    # 初始化数据探索器
    explorer = DataExplorer(output_dir="test_output")
    
    # 测试变量
    test_vars = ['feature1_normal', 'feature2_skewed', 'feature3_discrete', 'feature4_uniform']
    target_var = 'target'
    
    # 测试所有分箱方法
    methods = ['qcut', 'cut', 'tree', 'chi2']
    results = {}
    
    for method in methods:
        print(f"\n--- 测试分箱方法: {method} ---")
        try:
            result = explorer.create_binned_probability_analysis(
                df=df,
                continuous_vars=test_vars,
                target_var=target_var,
                n_bins=8,
                binning_method=method,
                min_bin_size=20,
                save_plots=False  # 不保存图表，只测试逻辑
            )
            
            results[method] = result
            
            # 打印结果摘要
            for var, var_result in result.items():
                print(f"  {var}:")
                print(f"    请求分箱数: {var_result['requested_bins']}")
                print(f"    实际分箱数: {var_result['actual_bins']}")
                print(f"    总样本数: {var_result['total_samples']}")
                if var_result['missing_data']:
                    print(f"    缺失样本数: {var_result['missing_data']['total_count']}")
                
                # 显示每个箱的信息
                grouped_data = var_result['grouped_data']
                print(f"    分箱详情:")
                for idx, row in grouped_data.iterrows():
                    print(f"      箱{idx+1}: 样本数={row['total_count']}, 正类数={row['positive_count']}, "
                          f"概率={row['probability']:.3f}, 平滑概率={row['probability_smoothed']:.3f}")
                
        except Exception as e:
            print(f"  方法 {method} 测试失败: {e}")
            import traceback
            traceback.print_exc()
    
    # 比较不同方法的结果
    print(f"\n=== 方法比较 ===")
    for var in test_vars:
        print(f"\n变量 {var} 的分箱比较:")
        for method in methods:
            if method in results and var in results[method]:
                var_result = results[method][var]
                print(f"  {method}: {var_result['actual_bins']}箱, "
                      f"总样本={var_result['total_samples']}")
    
    print(f"\n=== 测试完成 ===")
    return results

def test_edge_cases():
    """测试边界情况"""
    print(f"\n=== 边界情况测试 ===")
    
    explorer = DataExplorer(output_dir="test_output")
    
    # 测试1：很少的唯一值
    df1 = pd.DataFrame({
        'feature': [1, 1, 1, 2, 2, 3] * 10,
        'target': [0, 1, 0, 1, 1, 0] * 10
    })
    
    print("测试1: 很少的唯一值")
    try:
        result1 = explorer._analyze_single_variable(
            df1, 'feature', 'target', n_bins=10, binning_method='qcut', min_bin_size=5
        )
        print(f"  成功: 实际分箱数 = {result1['actual_bins']}")
    except Exception as e:
        print(f"  失败: {e}")
    
    # 测试2：极小样本
    df2 = pd.DataFrame({
        'feature': [1, 2, 3, 4, 5],
        'target': [0, 1, 0, 1, 1]
    })
    
    print("测试2: 极小样本")
    try:
        result2 = explorer._analyze_single_variable(
            df2, 'feature', 'target', n_bins=5, binning_method='tree', min_bin_size=2
        )
        print(f"  成功: 实际分箱数 = {result2['actual_bins']}")
    except Exception as e:
        print(f"  失败: {e}")
    
    # 测试3：全部缺失
    df3 = pd.DataFrame({
        'feature': [np.nan] * 10,
        'target': [0, 1] * 5
    })
    
    print("测试3: 全部缺失")
    try:
        result3 = explorer._analyze_single_variable(
            df3, 'feature', 'target', n_bins=5, binning_method='qcut', min_bin_size=2
        )
        if result3 is None:
            print("  正确处理: 返回None")
        else:
            print(f"  意外结果: {result3}")
    except Exception as e:
        print(f"  异常: {e}")

if __name__ == "__main__":
    # 运行测试
    test_results = test_binning_methods()
    test_edge_cases()
    
    print(f"\n=== 验证脚本执行完成 ===")
    print("如果没有严重错误，说明新的分箱方法实现基本正确")
    print("请检查输出结果是否符合预期")
