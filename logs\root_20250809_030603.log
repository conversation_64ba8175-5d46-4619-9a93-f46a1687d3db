2025-08-09 03:06:04 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 03:06:05 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 03:06:05 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-09 03:06:05 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-09 03:06:05 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-09 03:06:06 - GUI - INFO - GUI界面初始化完成
2025-08-09 03:06:37 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 03:06:37 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 03:06:39 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9794
2025-08-09 03:06:40 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9819
2025-08-09 03:06:43 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9836
2025-08-09 03:06:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 277, 'max_depth': 5, 'min_samples_split': 14, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9836
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 03:06:56 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 03:06:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_030656.html
2025-08-09 03:06:56 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 03:06:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_030656.html
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 19.72 秒
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 5.956601912774982, 'clf__kernel': 'rbf'}
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9884
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 03:06:57 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 03:06:57 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_030657.html
2025-08-09 03:06:57 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 03:06:57 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_030657.html
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.00 秒
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 03:07:03 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9857
2025-08-09 03:07:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.009365647299443846}
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 03:07:21 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 03:07:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_030721.html
2025-08-09 03:07:21 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 03:07:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_030721.html
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 23.89 秒
2025-08-09 03:07:25 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_030725
2025-08-09 03:07:25 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250809_030725 (ID: 20250809_030725)
2025-08-09 03:07:25 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250809_030725
2025-08-09 03:07:25 - session_utils - INFO - 创建新会话: 训练_nodule2_20250809_030725 (ID: 20250809_030725)
2025-08-09 03:07:25 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 03:07:25 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 03:07:25 - model_training - INFO - 模型名称: Random Forest
2025-08-09 03:07:25 - model_training - INFO - 准确率: 0.8750
2025-08-09 03:07:25 - model_training - INFO - AUC: 0.9412
2025-08-09 03:07:25 - model_training - INFO - AUPRC: 0.9359
2025-08-09 03:07:25 - model_training - INFO - 混淆矩阵:
2025-08-09 03:07:25 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 03:07:25 - model_training - INFO - 
分类报告:
2025-08-09 03:07:25 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 03:07:25 - model_training - INFO - 训练时间: 0.09 秒
2025-08-09 03:07:25 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 03:07:25 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_030725\models\RandomForest_single_030725.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_030725\models\RandomForest_single_030725.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型名称: SVM
2025-08-09 03:07:25 - model_training - INFO - 准确率: 0.7750
2025-08-09 03:07:25 - model_training - INFO - AUC: 0.9182
2025-08-09 03:07:25 - model_training - INFO - AUPRC: 0.9034
2025-08-09 03:07:25 - model_training - INFO - 混淆矩阵:
2025-08-09 03:07:25 - model_training - INFO - 
[[19  4]
 [ 5 12]]
2025-08-09 03:07:25 - model_training - INFO - 
分类报告:
2025-08-09 03:07:25 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.79      0.83      0.81        23
           1       0.75      0.71      0.73        17

    accuracy                           0.78        40
   macro avg       0.77      0.77      0.77        40
weighted avg       0.77      0.78      0.77        40

2025-08-09 03:07:25 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 03:07:25 - model_training - INFO - 模型 SVM 性能: 准确率=0.7750
2025-08-09 03:07:25 - training_session_manager - INFO - 保存模型 SVM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_030725\models\SVM_single_030725.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型 SVM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_030725\models\SVM_single_030725.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型 SVM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\SVM_results.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型名称: Neural Network
2025-08-09 03:07:25 - model_training - INFO - 准确率: 0.8750
2025-08-09 03:07:25 - model_training - INFO - AUC: 0.9591
2025-08-09 03:07:25 - model_training - INFO - AUPRC: 0.9450
2025-08-09 03:07:25 - model_training - INFO - 混淆矩阵:
2025-08-09 03:07:25 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 03:07:25 - model_training - INFO - 
分类报告:
2025-08-09 03:07:25 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 03:07:25 - model_training - INFO - 训练时间: 0.24 秒
2025-08-09 03:07:25 - model_training - INFO - 模型 NeuralNet 性能: 准确率=0.8750
2025-08-09 03:07:25 - training_session_manager - INFO - 保存模型 NeuralNet 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_030725\models\NeuralNet_single_030725.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型 NeuralNet 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_030725\models\NeuralNet_single_030725.joblib
2025-08-09 03:07:25 - model_training - INFO - 模型 NeuralNet 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\NeuralNet_results.joblib
2025-08-09 03:07:25 - delong_test - INFO - 开始DeLong检验，比较3个模型
2025-08-09 03:07:25 - delong_test - INFO - 比较RandomForest vs SVM: AUC1=0.9412, AUC2=0.9182, p=0.670889
2025-08-09 03:07:25 - delong_test - INFO - 比较RandomForest vs NeuralNet: AUC1=0.9412, AUC2=0.9591, p=0.681476
2025-08-09 03:07:25 - delong_test - INFO - 比较SVM vs NeuralNet: AUC1=0.9182, AUC2=0.9591, p=0.411875
2025-08-09 03:07:25 - delong_test - INFO - DeLong检验完成，共3个比较
