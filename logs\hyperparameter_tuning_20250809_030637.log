2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 03:06:37 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 03:06:39 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9794
2025-08-09 03:06:40 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9819
2025-08-09 03:06:43 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9836
2025-08-09 03:06:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:53 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:53 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 277, 'max_depth': 5, 'min_samples_split': 14, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9836
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 03:06:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_030656.html
2025-08-09 03:06:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_030656.html
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 19.72 秒
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 03:06:56 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 5.956601912774982, 'clf__kernel': 'rbf'}
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9884
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 03:06:57 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_030657.html
2025-08-09 03:06:57 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_030657.html
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.00 秒
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 03:06:57 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 03:07:03 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9857
2025-08-09 03:07:15 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:15 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:20 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:20 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.009365647299443846}
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9864
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 03:07:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_030721.html
2025-08-09 03:07:21 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_030721.html
2025-08-09 03:07:21 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 23.89 秒
