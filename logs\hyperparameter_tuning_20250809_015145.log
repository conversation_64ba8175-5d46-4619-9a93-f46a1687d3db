2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:51:45 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:51:47 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9786
2025-08-09 01:51:49 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9818
2025-08-09 01:51:51 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9835
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:01 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:01 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9836
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - Trial 22: 发现更好的得分 0.9852
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 147, 'max_depth': 11, 'min_samples_split': 7, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9852
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 实际执行试验次数: 24/50
2025-08-09 01:52:02 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:52:02 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_015202.html
2025-08-09 01:52:03 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_015203.html
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 17.42 秒
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 4.619272175049414, 'clf__kernel': 'rbf'}
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9876
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 01:52:03 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:52:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_015203.html
2025-08-09 01:52:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_015204.html
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.25 秒
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:52:04 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 3}
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9812
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:52:05 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_015205.html
2025-08-09 01:52:05 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_015205.html
2025-08-09 01:52:05 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.22 秒
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9274
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9430
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9430
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9430
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.9479
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9430
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9430
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 7, 'min_samples_split': 30, 'min_samples_leaf': 9, 'criterion': 'entropy', 'class_weight': 'balanced', 'max_features': None}
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9479
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/50
2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:53:04 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:04 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\optimization_history_20250809_015304.html
2025-08-09 01:53:05 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\DecisionTree\param_importances_20250809_015304.html
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.94 秒
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:53:05 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:53:09 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9786
2025-08-09 01:53:09 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9836
2025-08-09 01:53:16 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:16 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:17 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:17 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 222, 'max_depth': 23, 'min_samples_split': 17, 'min_samples_leaf': 1, 'max_features': 'sqrt'}
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9844
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/50
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:53:19 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_015319.html
2025-08-09 01:53:19 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_015319.html
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 14.73 秒
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:19 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9860
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9876
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9893
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:21 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:22 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 215, 'max_depth': 10, 'learning_rate': 0.217017658308203, 'subsample': 0.73022589840624, 'colsample_bytree': 0.5914857955622806}
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9893
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-09 01:53:23 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:53:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_015323.html
2025-08-09 01:53:24 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_015324.html
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.49 秒
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:53:24 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:53:31 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9823
2025-08-09 01:53:32 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9842
2025-08-09 01:53:32 - hyperparameter_tuning - INFO - Trial 12: 发现更好的得分 0.9866
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9866
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9866
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 88, 'max_depth': 4, 'learning_rate': 0.09954745639192011, 'feature_fraction': 0.933015774119652, 'bagging_fraction': 0.6692850364629274}
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9874
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 实际执行试验次数: 27/50
2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:53:33 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:33 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_015333.html
2025-08-09 01:53:34 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_015334.html
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 10.02 秒
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - CatBoost使用串行模式避免GPU设备冲突
2025-08-09 01:53:34 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-09 01:53:39 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9721
2025-08-09 01:53:40 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9820
2025-08-09 01:53:55 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9861
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9869
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 204, 'depth': 2, 'learning_rate': 0.11895186568849454, 'l2_leaf_reg': 8.448363213508443, 'bagging_temperature': 0.8095789183531145}
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9869
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:54:43 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:43 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\optimization_history_20250809_015443.html
2025-08-09 01:54:44 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\CatBoost\param_importances_20250809_015443.html
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 69.72 秒
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9706
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - Trial 7: 发现更好的得分 0.9731
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9747
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:44 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 9.081540944009888, 'clf__solver': 'lbfgs'}
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9747
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 实际执行试验次数: 22/50
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:54:45 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_015445.html
2025-08-09 01:54:45 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_015445.html
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.61 秒
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:54:45 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9688
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9868
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 5.756578470029271, 'clf__kernel': 'rbf'}
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9884
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:54:46 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:46 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\optimization_history_20250809_015446.html
2025-08-09 01:54:47 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\SVM\param_importances_20250809_015446.html
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.27 秒
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9782
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - Trial 8: 发现更好的得分 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 4}
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9816
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 实际执行试验次数: 26/50
2025-08-09 01:54:47 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:54:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\optimization_history_20250809_015447.html
2025-08-09 01:54:48 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\KNN\param_importances_20250809_015448.html
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.41 秒
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9573
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 01:54:48 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 01:54:55 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9857
2025-08-09 01:55:04 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:04 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9857
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.008391381121781416}
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9864
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-09 01:55:06 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 01:55:07 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:55:07 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\optimization_history_20250809_015506.html
2025-08-09 01:55:07 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 01:55:07 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\NeuralNet\param_importances_20250809_015507.html
2025-08-09 01:55:07 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 18.92 秒
