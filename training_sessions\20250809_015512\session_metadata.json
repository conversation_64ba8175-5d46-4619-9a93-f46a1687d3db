{"session_id": "20250809_015512", "session_name": "训练_nodule2_20250809_015512", "description": "自动创建的训练会话，基于数据文件: nodule2", "created_time": "2025-08-09T01:55:12.327464", "last_modified": "2025-08-09T01:55:14.094038", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_015512.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_015512\\models\\DecisionTree_single_015512.joblib", "save_time": "2025-08-09T01:55:12.368923"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_015512.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_015512\\models\\RandomForest_single_015512.joblib", "save_time": "2025-08-09T01:55:12.471260"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_015512.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_015512\\models\\XGBoost_single_015512.joblib", "save_time": "2025-08-09T01:55:12.570854"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_015512.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_015512\\models\\LightGBM_single_015512.joblib", "save_time": "2025-08-09T01:55:12.638924"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_015513.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_015512\\models\\CatBoost_single_015513.joblib", "save_time": "2025-08-09T01:55:13.700297"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_015513.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_015512\\models\\Logistic_single_015513.joblib", "save_time": "2025-08-09T01:55:13.754659"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_015513.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_015512\\models\\SVM_single_015513.joblib", "save_time": "2025-08-09T01:55:13.784461"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_015513.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_015512\\models\\KNN_single_015513.joblib", "save_time": "2025-08-09T01:55:13.813955"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_015513.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_015512\\models\\NaiveBayes_single_015513.joblib", "save_time": "2025-08-09T01:55:13.842502"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_015514.joblib", "filepath": "c:\\Users\\<USER>\\PycharmProjects\\multi_model_01_updated\\training_sessions\\20250809_015512\\models\\NeuralNet_single_015514.joblib", "save_time": "2025-08-09T01:55:14.087135"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}