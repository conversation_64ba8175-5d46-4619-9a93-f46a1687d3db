2025-08-09 22:55:48 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 22:55:48 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 22:55:48 - GUI - INFO - 字体管理器初始化成功，使用英文字体
2025-08-09 22:55:49 - data_exploration - INFO - 数据探索器初始化完成，输出目录: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\data_exploration
2025-08-09 22:55:49 - gui_data_exploration - INFO - 数据探索GUI组件初始化完成
2025-08-09 22:55:49 - GUI - INFO - GUI界面初始化完成
2025-08-09 22:56:35 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 22:56:35 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 100
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 22:56:35 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 22:56:38 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9828
2025-08-09 22:56:42 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9844
2025-08-09 22:56:51 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:51 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:52 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:52 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:54 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:54 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 204, 'max_depth': 32, 'min_samples_split': 3, 'min_samples_leaf': 2, 'max_features': 'sqrt'}
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9844
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 实际执行试验次数: 27/100
2025-08-09 22:56:55 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 22:56:56 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 22:56:56 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:56:56 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\optimization_history_20250809_225656.html
2025-08-09 22:56:56 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 22:56:57 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\RandomForest\param_importances_20250809_225656.html
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 21.18 秒
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 100
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:57 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9860
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9892
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:58 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:56:59 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9899
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 153, 'max_depth': 9, 'learning_rate': 0.2676604216316705, 'subsample': 0.6253646588107045, 'colsample_bytree': 0.6510423826285919}
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9899
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/100
2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 22:57:00 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 22:57:00 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:00 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\optimization_history_20250809_225700.html
2025-08-09 22:57:00 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 22:57:01 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\XGBoost\param_importances_20250809_225700.html
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.04 秒
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 100
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 22:57:01 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 22:57:08 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9843
2025-08-09 22:57:08 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9883
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 281, 'max_depth': 7, 'learning_rate': 0.26511448016990713, 'feature_fraction': 0.6878665519661848, 'bagging_fraction': 0.8248376205582659}
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9884
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 实际执行试验次数: 20/100
2025-08-09 22:57:09 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 22:57:09 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 22:57:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\optimization_history_20250809_225709.html
2025-08-09 22:57:10 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 22:57:10 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\LightGBM\param_importances_20250809_225710.html
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.43 秒
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 100
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 8
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 100, 'n_jobs': 8, 'timeout': 1800}
2025-08-09 22:57:10 - hyperparameter_tuning - INFO - Trial 3: 发现更好的得分 0.9739
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9740
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9747
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 9.117827635525941, 'clf__solver': 'lbfgs'}
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9747
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/100
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-09 22:57:11 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 22:57:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\optimization_history_20250809_225711.html
2025-08-09 22:57:11 - choreographer.browsers.chromium - INFO - Chromium init'ed with kwargs {}
2025-08-09 22:57:11 - hyperparameter_tuning - WARNING - 使用kaleido引擎保存图像失败: 

Kaleido requires Google Chrome to be installed.

Either download and install Chrome yourself following Google's instructions for your operating system,
or install it from your terminal by running:

    $ plotly_get_chrome


2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML格式到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\output\Logistic\param_importances_20250809_225711.html
2025-08-09 22:57:11 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.23 秒
2025-08-09 22:57:37 - training_session_manager - INFO - 创建会话目录结构: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737
2025-08-09 22:57:37 - training_session_manager - INFO - 创建训练会话: 训练_nodule2_20250809_225737 (ID: 20250809_225737)
2025-08-09 22:57:37 - training_session_manager - INFO - 创建新会话: 训练_nodule2_20250809_225737
2025-08-09 22:57:37 - session_utils - INFO - 创建新会话: 训练_nodule2_20250809_225737 (ID: 20250809_225737)
2025-08-09 22:57:37 - data_preprocessing - INFO - 成功加载 数据，特征维度: (197, 8)
2025-08-09 22:57:37 - data_preprocessing - INFO - 应用了 standard 特征缩放
2025-08-09 22:57:37 - model_training - INFO - 模型名称: Random Forest
2025-08-09 22:57:37 - model_training - INFO - 准确率: 0.8750
2025-08-09 22:57:37 - model_training - INFO - AUC: 0.9412
2025-08-09 22:57:37 - model_training - INFO - AUPRC: 0.9359
2025-08-09 22:57:37 - model_training - INFO - 混淆矩阵:
2025-08-09 22:57:37 - model_training - INFO - 
[[21  2]
 [ 3 14]]
2025-08-09 22:57:37 - model_training - INFO - 
分类报告:
2025-08-09 22:57:37 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.88      0.91      0.89        23
           1       0.88      0.82      0.85        17

    accuracy                           0.88        40
   macro avg       0.88      0.87      0.87        40
weighted avg       0.88      0.88      0.87        40

2025-08-09 22:57:37 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 22:57:37 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8750
2025-08-09 22:57:37 - training_session_manager - INFO - 保存模型 RandomForest 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\RandomForest_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 RandomForest 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\RandomForest_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 RandomForest 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 22:57:37 - model_training - INFO - [XGBoost] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 22:57:37 - model_training - INFO - 模型名称: XGBoost
2025-08-09 22:57:37 - model_training - INFO - 准确率: 0.9000
2025-08-09 22:57:37 - model_training - INFO - AUC: 0.9719
2025-08-09 22:57:37 - model_training - INFO - AUPRC: 0.9627
2025-08-09 22:57:37 - model_training - INFO - 混淆矩阵:
2025-08-09 22:57:37 - model_training - INFO - 
[[21  2]
 [ 2 15]]
2025-08-09 22:57:37 - model_training - INFO - 
分类报告:
2025-08-09 22:57:37 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.91      0.91      0.91        23
           1       0.88      0.88      0.88        17

    accuracy                           0.90        40
   macro avg       0.90      0.90      0.90        40
weighted avg       0.90      0.90      0.90        40

2025-08-09 22:57:37 - model_training - INFO - 训练时间: 0.05 秒
2025-08-09 22:57:37 - model_training - INFO - 模型 XGBoost 性能: 准确率=0.9000
2025-08-09 22:57:37 - training_session_manager - INFO - 保存模型 XGBoost 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\XGBoost_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 XGBoost 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\XGBoost_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 XGBoost 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\XGBoost_results.joblib
2025-08-09 22:57:37 - model_training - INFO - [LightGBM] 自动设置 scale_pos_weight=1.309 以处理不平衡
2025-08-09 22:57:37 - model_training - INFO - 模型名称: LightGBM
2025-08-09 22:57:37 - model_training - INFO - 准确率: 0.8500
2025-08-09 22:57:37 - model_training - INFO - AUC: 0.9488
2025-08-09 22:57:37 - model_training - INFO - AUPRC: 0.9492
2025-08-09 22:57:37 - model_training - INFO - 混淆矩阵:
2025-08-09 22:57:37 - model_training - INFO - 
[[20  3]
 [ 3 14]]
2025-08-09 22:57:37 - model_training - INFO - 
分类报告:
2025-08-09 22:57:37 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.87      0.87      0.87        23
           1       0.82      0.82      0.82        17

    accuracy                           0.85        40
   macro avg       0.85      0.85      0.85        40
weighted avg       0.85      0.85      0.85        40

2025-08-09 22:57:37 - model_training - INFO - 训练时间: 0.04 秒
2025-08-09 22:57:37 - model_training - INFO - 模型 LightGBM 性能: 准确率=0.8500
2025-08-09 22:57:37 - training_session_manager - INFO - 保存模型 LightGBM 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\LightGBM_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 LightGBM 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\LightGBM_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 LightGBM 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\LightGBM_results.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:57:37 - model_training - INFO - 准确率: 0.8250
2025-08-09 22:57:37 - model_training - INFO - AUC: 0.9284
2025-08-09 22:57:37 - model_training - INFO - AUPRC: 0.9288
2025-08-09 22:57:37 - model_training - INFO - 混淆矩阵:
2025-08-09 22:57:37 - model_training - INFO - 
[[20  3]
 [ 4 13]]
2025-08-09 22:57:37 - model_training - INFO - 
分类报告:
2025-08-09 22:57:37 - model_training - INFO - 
              precision    recall  f1-score   support

           0       0.83      0.87      0.85        23
           1       0.81      0.76      0.79        17

    accuracy                           0.82        40
   macro avg       0.82      0.82      0.82        40
weighted avg       0.82      0.82      0.82        40

2025-08-09 22:57:37 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:57:37 - model_training - INFO - 模型 Logistic 性能: 准确率=0.8250
2025-08-09 22:57:37 - training_session_manager - INFO - 保存模型 Logistic 到: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\Logistic_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 Logistic 已保存到会话: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\training_sessions\20250809_225737\models\Logistic_single_225737.joblib
2025-08-09 22:57:37 - model_training - INFO - 模型 Logistic 同时保存到缓存: c:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:57:37 - delong_test - INFO - 开始DeLong检验，比较4个模型
2025-08-09 22:57:37 - delong_test - INFO - 比较RandomForest vs XGBoost: AUC1=0.9412, AUC2=0.9719, p=0.442730
2025-08-09 22:57:37 - delong_test - INFO - 比较RandomForest vs LightGBM: AUC1=0.9412, AUC2=0.9488, p=0.871470
2025-08-09 22:57:37 - delong_test - INFO - 比较RandomForest vs Logistic: AUC1=0.9412, AUC2=0.9284, p=0.808351
2025-08-09 22:57:37 - delong_test - INFO - 比较XGBoost vs LightGBM: AUC1=0.9719, AUC2=0.9488, p=0.551970
2025-08-09 22:57:37 - delong_test - INFO - 比较XGBoost vs Logistic: AUC1=0.9719, AUC2=0.9284, p=0.334321
2025-08-09 22:57:37 - delong_test - INFO - 比较LightGBM vs Logistic: AUC1=0.9488, AUC2=0.9284, p=0.692589
2025-08-09 22:57:37 - delong_test - INFO - DeLong检验完成，共6个比较
