2025-08-09 22:45:54 - training_session_manager - INFO - 初始化训练会话管理器
2025-08-09 22:45:54 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:45:54 - model_training - INFO - 准确率: 1.0000
2025-08-09 22:45:54 - model_training - INFO - AUC: 1.0000
2025-08-09 22:45:54 - model_training - INFO - AUPRC: 1.0000
2025-08-09 22:45:54 - model_training - INFO - 混淆矩阵:
2025-08-09 22:45:54 - model_training - INFO - 
[[ 7  0]
 [ 0 13]]
2025-08-09 22:45:54 - model_training - INFO - 
分类报告:
2025-08-09 22:45:54 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      1.00      1.00         7
           1       1.00      1.00      1.00        13

    accuracy                           1.00        20
   macro avg       1.00      1.00      1.00        20
weighted avg       1.00      1.00      1.00        20

2025-08-09 22:45:54 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:45:54 - model_training - INFO - 模型 Logistic 性能: 准确率=1.0000
2025-08-09 22:45:54 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:45:54 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:45:54 - model_ensemble - INFO - SHAP库已成功导入，可解释性分析功能已启用
2025-08-09 22:45:55 - model_ensemble - INFO - ============================================================
2025-08-09 22:45:55 - model_ensemble - INFO - 开始运行集成学习管道
2025-08-09 22:45:55 - model_ensemble - INFO - ============================================================
2025-08-09 22:45:55 - model_ensemble - INFO - 基础模型: ['Logistic', 'RandomForest']
2025-08-09 22:45:55 - model_ensemble - INFO - 集成方法: ['voting']
2025-08-09 22:45:55 - model_ensemble - INFO - 步骤1: 训练基础模型
2025-08-09 22:45:55 - model_ensemble - INFO - 训练基础模型: Logistic
2025-08-09 22:45:55 - model_training - INFO - 模型名称: Logistic Regression
2025-08-09 22:45:55 - model_training - INFO - 准确率: 1.0000
2025-08-09 22:45:55 - model_training - INFO - AUC: 1.0000
2025-08-09 22:45:55 - model_training - INFO - AUPRC: 1.0000
2025-08-09 22:45:55 - model_training - INFO - 混淆矩阵:
2025-08-09 22:45:55 - model_training - INFO - 
[[ 7  0]
 [ 0 13]]
2025-08-09 22:45:55 - model_training - INFO - 
分类报告:
2025-08-09 22:45:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      1.00      1.00         7
           1       1.00      1.00      1.00        13

    accuracy                           1.00        20
   macro avg       1.00      1.00      1.00        20
weighted avg       1.00      1.00      1.00        20

2025-08-09 22:45:55 - model_training - INFO - 训练时间: 0.01 秒
2025-08-09 22:45:55 - model_training - INFO - 模型 Logistic 性能: 准确率=1.0000
2025-08-09 22:45:55 - model_training - INFO - 模型 Logistic 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_results.joblib
2025-08-09 22:45:55 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\Logistic_feature_names.joblib
2025-08-09 22:45:55 - model_ensemble - INFO -   Logistic 训练完成
2025-08-09 22:45:55 - model_ensemble - INFO - 训练基础模型: RandomForest
2025-08-09 22:45:55 - model_training - INFO - 模型名称: Random Forest
2025-08-09 22:45:55 - model_training - INFO - 准确率: 0.8000
2025-08-09 22:45:55 - model_training - INFO - AUC: 0.9011
2025-08-09 22:45:55 - model_training - INFO - AUPRC: 0.9387
2025-08-09 22:45:55 - model_training - INFO - 混淆矩阵:
2025-08-09 22:45:55 - model_training - INFO - 
[[ 3  4]
 [ 0 13]]
2025-08-09 22:45:55 - model_training - INFO - 
分类报告:
2025-08-09 22:45:55 - model_training - INFO - 
              precision    recall  f1-score   support

           0       1.00      0.43      0.60         7
           1       0.76      1.00      0.87        13

    accuracy                           0.80        20
   macro avg       0.88      0.71      0.73        20
weighted avg       0.85      0.80      0.77        20

2025-08-09 22:45:55 - model_training - INFO - 训练时间: 0.08 秒
2025-08-09 22:45:55 - model_training - INFO - 模型 RandomForest 性能: 准确率=0.8000
2025-08-09 22:45:55 - model_training - INFO - 模型 RandomForest 的结果已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_results.joblib
2025-08-09 22:45:55 - model_training - INFO - 特征名称已缓存到: C:\Users\<USER>\PycharmProjects\multi_model_01_updated\cache\RandomForest_feature_names.joblib
2025-08-09 22:45:55 - model_ensemble - INFO -   RandomForest 训练完成
2025-08-09 22:45:55 - model_ensemble - INFO - 成功训练了 2 个基础模型
2025-08-09 22:45:55 - model_ensemble - INFO - 步骤2: 运行集成方法 - voting
2025-08-09 22:45:55 - model_ensemble - INFO -   训练 voting_soft 集成模型
2025-08-09 22:45:55 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-09 22:45:55 - model_ensemble - INFO -     voting_soft - 准确率: 0.9000, F1: 0.8952
2025-08-09 22:45:55 - model_ensemble - INFO -   训练 voting_hard 集成模型
2025-08-09 22:45:55 - model_ensemble - INFO - 开始训练集成模型，方法: voting
2025-08-09 22:45:55 - model_ensemble - INFO -     voting_hard - 准确率: 1.0000, F1: 1.0000
2025-08-09 22:45:55 - model_ensemble - INFO - ============================================================
2025-08-09 22:45:55 - model_ensemble - INFO - 集成学习结果总结
2025-08-09 22:45:55 - model_ensemble - INFO - ============================================================
2025-08-09 22:45:55 - model_ensemble - INFO - 最佳集成模型: voting_hard
2025-08-09 22:45:55 - model_ensemble - INFO - 最佳F1分数: 1.0000
2025-08-09 22:45:55 - model_ensemble - INFO - 最佳准确率: 1.0000
2025-08-09 22:45:55 - model_ensemble - INFO - 
所有集成模型性能对比:
2025-08-09 22:45:55 - model_ensemble - INFO -   voting_soft     - 准确率: 0.9000, 精确率: 0.9133, 召回率: 0.9000, F1: 0.8952, AUC: 1.0000
2025-08-09 22:45:55 - model_ensemble - INFO -   voting_hard     - 准确率: 1.0000, 精确率: 1.0000, 召回率: 1.0000, F1: 1.0000, AUC: 0.0000
2025-08-09 22:45:55 - external_validation - INFO - 成功加载模型 Logistic
2025-08-09 22:45:55 - external_validation - INFO - 应用训练时的数据缩放器到外部验证数据
2025-08-09 22:45:55 - external_validation - INFO - 外部验证结果:
2025-08-09 22:45:55 - external_validation - INFO - 准确率: 1.0000
2025-08-09 22:45:55 - external_validation - INFO - 精确率: 1.0000
2025-08-09 22:45:55 - external_validation - INFO - 召回率: 1.0000
2025-08-09 22:45:55 - external_validation - INFO - 特异性: 1.0000
2025-08-09 22:45:55 - external_validation - INFO - F1分数: 1.0000
2025-08-09 22:45:55 - external_validation - INFO - AUC: 1.0000
2025-08-09 22:45:55 - external_validation - INFO - 混淆矩阵:
2025-08-09 22:45:55 - external_validation - INFO - [[4 0]
 [0 6]]
