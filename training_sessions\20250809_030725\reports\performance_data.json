{"generation_time": "2025-08-09T03:11:53.013865", "best_model": "NeuralNet", "best_score": 0.6194511358962005, "model_count": 3, "detailed_metrics": {"NeuralNet": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.0, "auc_pr": 0.0, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876}, "SVM": {"accuracy": 0.775, "precision": 0.75, "recall": 0.7058823529411765, "f1_score": 0.7272727272727273, "specificity": 0.8260869565217391, "sensitivity": 0.7058823529411765, "npv": 0.7916666666666666, "ppv": 0.75, "auc_roc": 0.0, "auc_pr": 0.0, "mcc": 0.5367960903599671, "kappa": 0.5360824742268041, "balanced_accuracy": 0.7659846547314578}, "RandomForest": {"accuracy": 0.875, "precision": 0.875, "recall": 0.8235294117647058, "f1_score": 0.8484848484848485, "specificity": 0.9130434782608695, "sensitivity": 0.8235294117647058, "npv": 0.875, "ppv": 0.875, "auc_roc": 0.0, "auc_pr": 0.0, "mcc": 0.7432561251138006, "kappa": 0.7422680412371134, "balanced_accuracy": 0.8682864450127876}}, "ranking": [["NeuralNet", 0.6194511358962005], ["RandomForest", 0.6194511358962005], ["SVM", 0.5146519988206566]]}