# 数据探索分析报告

## 数据概览
- 数据形状: 197 行 × 9 列
- 目标变量: label
- 分析变量: v, s, ctr, ctmean, ctmax, ctmin, ctsd, iqr
- 生成时间: 2025-08-10 01:02:01

## 目标变量分布

### 类别分布
- label = 0: 112 样本 (56.9%)
- label = 1: 85 样本 (43.1%)

## 分组概率分析结果


### v 分组分析
- 分箱方法: chi2
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 50 | 0 | 0.000 | [0.000, 0.060] |
| Group 2 | 10 | 1 | 0.100 | [0.018, 0.404] |
| Group 3 | 9 | 0 | 0.000 | [0.000, 0.333] |
| Group 4 | 30 | 5 | 0.167 | [0.073, 0.336] |
| Group 5 | 9 | 4 | 0.444 | [0.189, 0.733] |
| Group 6 | 40 | 30 | 0.750 | [0.598, 0.858] |
| Group 7 | 9 | 7 | 0.778 | [0.453, 0.937] |
| Group 8 | 10 | 9 | 0.900 | [0.596, 0.982] |
| Group 9 | 20 | 20 | 1.000 | [0.850, 1.000] |
| Group 10 | 10 | 9 | 0.900 | [0.596, 0.982] |

### s 分组分析
- 分箱方法: chi2
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 20 | 18 | 0.900 | [0.699, 0.972] |
| Group 2 | 10 | 10 | 1.000 | [0.700, 1.000] |
| Group 3 | 20 | 17 | 0.850 | [0.640, 0.948] |
| Group 4 | 40 | 28 | 0.700 | [0.546, 0.819] |
| Group 5 | 9 | 3 | 0.333 | [0.121, 0.646] |
| Group 6 | 39 | 5 | 0.128 | [0.056, 0.267] |
| Group 7 | 10 | 3 | 0.300 | [0.108, 0.603] |
| Group 8 | 10 | 0 | 0.000 | [0.000, 0.300] |
| Group 9 | 19 | 1 | 0.053 | [0.009, 0.246] |
| Group 10 | 10 | 0 | 0.000 | [0.000, 0.300] |

### ctr 分组分析
- 分箱方法: chi2
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 40 | 5 | 0.125 | [0.055, 0.261] |
| Group 2 | 11 | 7 | 0.636 | [0.354, 0.848] |
| Group 3 | 8 | 7 | 0.875 | [0.529, 0.978] |
| Group 4 | 40 | 27 | 0.675 | [0.520, 0.799] |
| Group 5 | 9 | 3 | 0.333 | [0.121, 0.646] |
| Group 6 | 19 | 11 | 0.579 | [0.363, 0.769] |
| Group 7 | 41 | 13 | 0.317 | [0.196, 0.470] |
| Group 8 | 10 | 2 | 0.200 | [0.057, 0.510] |
| Group 9 | 9 | 6 | 0.667 | [0.354, 0.879] |
| Group 10 | 10 | 4 | 0.400 | [0.168, 0.687] |

### ctmean 分组分析
- 分箱方法: chi2
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 30 | 3 | 0.100 | [0.035, 0.256] |
| Group 2 | 29 | 19 | 0.655 | [0.473, 0.801] |
| Group 3 | 10 | 5 | 0.500 | [0.237, 0.763] |
| Group 4 | 20 | 17 | 0.850 | [0.640, 0.948] |
| Group 5 | 9 | 4 | 0.444 | [0.189, 0.733] |
| Group 6 | 30 | 14 | 0.467 | [0.302, 0.639] |
| Group 7 | 39 | 11 | 0.282 | [0.165, 0.438] |
| Group 8 | 10 | 5 | 0.500 | [0.237, 0.763] |
| Group 9 | 10 | 2 | 0.200 | [0.057, 0.510] |
| Group 10 | 10 | 5 | 0.500 | [0.237, 0.763] |

### ctmax 分组分析
- 分箱方法: chi2
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 20 | 0 | 0.000 | [0.000, 0.150] |
| Group 2 | 39 | 9 | 0.231 | [0.126, 0.383] |
| Group 3 | 10 | 5 | 0.500 | [0.237, 0.763] |
| Group 4 | 20 | 3 | 0.150 | [0.052, 0.360] |
| Group 5 | 10 | 4 | 0.400 | [0.168, 0.687] |
| Group 6 | 9 | 2 | 0.222 | [0.063, 0.547] |
| Group 7 | 10 | 4 | 0.400 | [0.168, 0.687] |
| Group 8 | 10 | 10 | 1.000 | [0.700, 1.000] |
| Group 9 | 59 | 43 | 0.729 | [0.604, 0.826] |
| Group 10 | 10 | 5 | 0.500 | [0.237, 0.763] |

### ctmin 分组分析
- 分箱方法: chi2
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 31 | 31 | 1.000 | [0.903, 1.000] |
| Group 2 | 28 | 25 | 0.893 | [0.728, 0.963] |
| Group 3 | 10 | 6 | 0.600 | [0.313, 0.832] |
| Group 4 | 10 | 2 | 0.200 | [0.057, 0.510] |
| Group 5 | 10 | 0 | 0.000 | [0.000, 0.300] |
| Group 6 | 10 | 1 | 0.100 | [0.018, 0.404] |
| Group 7 | 9 | 2 | 0.222 | [0.063, 0.547] |
| Group 8 | 10 | 7 | 0.700 | [0.397, 0.892] |
| Group 9 | 10 | 0 | 0.000 | [0.000, 0.300] |
| Group 10 | 10 | 1 | 0.100 | [0.018, 0.404] |

### ctsd 分组分析
- 分箱方法: chi2
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 10 | 0 | 0.000 | [0.000, 0.300] |
| Group 2 | 10 | 1 | 0.100 | [0.018, 0.404] |
| Group 3 | 39 | 10 | 0.256 | [0.146, 0.411] |
| Group 4 | 10 | 6 | 0.600 | [0.313, 0.832] |
| Group 5 | 10 | 3 | 0.300 | [0.108, 0.603] |
| Group 6 | 10 | 6 | 0.600 | [0.313, 0.832] |
| Group 7 | 39 | 17 | 0.436 | [0.293, 0.590] |
| Group 8 | 39 | 23 | 0.590 | [0.434, 0.729] |
| Group 9 | 20 | 14 | 0.700 | [0.481, 0.855] |
| Group 10 | 10 | 5 | 0.500 | [0.237, 0.763] |

### iqr 分组分析
- 分箱方法: chi2
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 20 | 2 | 0.100 | [0.028, 0.301] |
| Group 2 | 39 | 14 | 0.359 | [0.227, 0.516] |
| Group 3 | 31 | 21 | 0.677 | [0.501, 0.814] |
| Group 4 | 9 | 2 | 0.222 | [0.063, 0.547] |
| Group 5 | 9 | 5 | 0.556 | [0.267, 0.811] |
| Group 6 | 10 | 6 | 0.600 | [0.313, 0.832] |
| Group 7 | 20 | 6 | 0.300 | [0.145, 0.519] |
| Group 8 | 10 | 7 | 0.700 | [0.397, 0.892] |
| Group 9 | 29 | 15 | 0.517 | [0.344, 0.686] |
| Group 10 | 10 | 3 | 0.300 | [0.108, 0.603] |

## 相关性分析结果

### 与目标变量的相关性排序
- s: -0.679
- ctmin: -0.580
- ctmax: 0.357
- v: 0.326
- ctsd: 0.260
- iqr: 0.086
- ctr: -0.046
- ctmean: -0.042

### 强相关变量 (|r| > 0.3)
- s: -0.679
- ctmin: -0.580
- ctmax: 0.357
- v: 0.326
