# 数据探索分析报告

## 数据概览
- 数据形状: 197 行 × 9 列
- 目标变量: label
- 分析变量: v, s, ctr, ctmean, ctmax, ctmin, ctsd, iqr
- 生成时间: 2025-08-10 00:07:57

## 目标变量分布

### 类别分布
- label = 0: 112 样本 (56.9%)
- label = 1: 85 样本 (43.1%)

## 分组概率分析结果


### v 分组分析
- 分箱方法: qcut
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 20 | 0 | 0.000 | [0.000, 0.150] |
| Group 2 | 20 | 0 | 0.000 | [0.000, 0.150] |
| Group 3 | 20 | 1 | 0.050 | [0.009, 0.236] |
| Group 4 | 19 | 1 | 0.053 | [0.009, 0.246] |
| Group 5 | 20 | 4 | 0.200 | [0.081, 0.416] |
| Group 6 | 19 | 10 | 0.526 | [0.317, 0.727] |
| Group 7 | 20 | 15 | 0.750 | [0.531, 0.888] |
| Group 8 | 19 | 16 | 0.842 | [0.624, 0.945] |
| Group 9 | 20 | 19 | 0.950 | [0.764, 0.991] |
| Group 10 | 20 | 19 | 0.950 | [0.764, 0.991] |

### s 分组分析
- 分箱方法: qcut
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 20 | 18 | 0.900 | [0.699, 0.972] |
| Group 2 | 20 | 19 | 0.950 | [0.764, 0.991] |
| Group 3 | 19 | 14 | 0.737 | [0.512, 0.882] |
| Group 4 | 20 | 14 | 0.700 | [0.481, 0.855] |
| Group 5 | 20 | 11 | 0.550 | [0.342, 0.742] |
| Group 6 | 19 | 3 | 0.158 | [0.055, 0.376] |
| Group 7 | 20 | 2 | 0.100 | [0.028, 0.301] |
| Group 8 | 19 | 3 | 0.158 | [0.055, 0.376] |
| Group 9 | 20 | 1 | 0.050 | [0.009, 0.236] |
| Group 10 | 20 | 0 | 0.000 | [0.000, 0.150] |

### ctr 分组分析
- 分箱方法: qcut
- 分组数量: 9
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 40 | 5 | 0.125 | [0.055, 0.261] |
| Group 2 | 19 | 14 | 0.737 | [0.512, 0.882] |
| Group 3 | 20 | 14 | 0.700 | [0.481, 0.855] |
| Group 4 | 20 | 13 | 0.650 | [0.433, 0.819] |
| Group 5 | 19 | 9 | 0.474 | [0.273, 0.683] |
| Group 6 | 20 | 9 | 0.450 | [0.258, 0.658] |
| Group 7 | 19 | 6 | 0.316 | [0.154, 0.540] |
| Group 8 | 21 | 5 | 0.238 | [0.106, 0.451] |
| Group 9 | 19 | 10 | 0.526 | [0.317, 0.727] |

### ctmean 分组分析
- 分箱方法: qcut
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 20 | 2 | 0.100 | [0.028, 0.301] |
| Group 2 | 20 | 7 | 0.350 | [0.181, 0.567] |
| Group 3 | 19 | 13 | 0.684 | [0.460, 0.846] |
| Group 4 | 20 | 14 | 0.700 | [0.481, 0.855] |
| Group 5 | 20 | 13 | 0.650 | [0.433, 0.819] |
| Group 6 | 19 | 8 | 0.421 | [0.231, 0.637] |
| Group 7 | 20 | 8 | 0.400 | [0.219, 0.613] |
| Group 8 | 19 | 6 | 0.316 | [0.154, 0.540] |
| Group 9 | 20 | 7 | 0.350 | [0.181, 0.567] |
| Group 10 | 20 | 7 | 0.350 | [0.181, 0.567] |

### ctmax 分组分析
- 分箱方法: qcut
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 20 | 0 | 0.000 | [0.000, 0.150] |
| Group 2 | 20 | 5 | 0.250 | [0.112, 0.469] |
| Group 3 | 19 | 4 | 0.211 | [0.085, 0.433] |
| Group 4 | 20 | 6 | 0.300 | [0.145, 0.519] |
| Group 5 | 20 | 6 | 0.300 | [0.145, 0.519] |
| Group 6 | 19 | 6 | 0.316 | [0.154, 0.540] |
| Group 7 | 20 | 18 | 0.900 | [0.699, 0.972] |
| Group 8 | 19 | 13 | 0.684 | [0.460, 0.846] |
| Group 9 | 20 | 14 | 0.700 | [0.481, 0.855] |
| Group 10 | 20 | 13 | 0.650 | [0.433, 0.819] |

### ctmin 分组分析
- 分箱方法: qcut
- 分组数量: 9
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 40 | 39 | 0.975 | [0.871, 0.996] |
| Group 2 | 19 | 17 | 0.895 | [0.686, 0.971] |
| Group 3 | 20 | 8 | 0.400 | [0.219, 0.613] |
| Group 4 | 20 | 1 | 0.050 | [0.009, 0.236] |
| Group 5 | 19 | 9 | 0.474 | [0.273, 0.683] |
| Group 6 | 20 | 1 | 0.050 | [0.009, 0.236] |
| Group 7 | 19 | 3 | 0.158 | [0.055, 0.376] |
| Group 8 | 21 | 3 | 0.143 | [0.050, 0.346] |
| Group 9 | 19 | 4 | 0.211 | [0.085, 0.433] |

### ctsd 分组分析
- 分箱方法: qcut
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 20 | 1 | 0.050 | [0.009, 0.236] |
| Group 2 | 20 | 5 | 0.250 | [0.112, 0.469] |
| Group 3 | 19 | 5 | 0.263 | [0.118, 0.488] |
| Group 4 | 20 | 9 | 0.450 | [0.258, 0.658] |
| Group 5 | 20 | 10 | 0.500 | [0.299, 0.701] |
| Group 6 | 19 | 8 | 0.421 | [0.231, 0.637] |
| Group 7 | 20 | 11 | 0.550 | [0.342, 0.742] |
| Group 8 | 19 | 11 | 0.579 | [0.363, 0.769] |
| Group 9 | 20 | 13 | 0.650 | [0.433, 0.819] |
| Group 10 | 20 | 12 | 0.600 | [0.387, 0.781] |

### iqr 分组分析
- 分箱方法: qcut
- 分组数量: 10
- 总体概率: 0.431

| 分组 | 样本数 | 阳性数 | 概率 | 95%置信区间 |
|------|--------|--------|------|-------------|
| Group 1 | 20 | 2 | 0.100 | [0.028, 0.301] |
| Group 2 | 20 | 7 | 0.350 | [0.181, 0.567] |
| Group 3 | 19 | 7 | 0.368 | [0.191, 0.590] |
| Group 4 | 20 | 14 | 0.700 | [0.481, 0.855] |
| Group 5 | 20 | 9 | 0.450 | [0.258, 0.658] |
| Group 6 | 19 | 11 | 0.579 | [0.363, 0.769] |
| Group 7 | 20 | 6 | 0.300 | [0.145, 0.519] |
| Group 8 | 19 | 10 | 0.526 | [0.317, 0.727] |
| Group 9 | 20 | 12 | 0.600 | [0.387, 0.781] |
| Group 10 | 20 | 7 | 0.350 | [0.181, 0.567] |

## 相关性分析结果

### 与目标变量的相关性排序
- s: -0.679
- ctmin: -0.580
- ctmax: 0.357
- v: 0.326
- ctsd: 0.260
- iqr: 0.086
- ctr: -0.046
- ctmean: -0.042

### 强相关变量 (|r| > 0.3)
- s: -0.679
- ctmin: -0.580
- ctmax: 0.357
- v: 0.326
